// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get appTitle => 'تلفزيون القطط';

  @override
  String get helloWorld => 'مرحبا بالعالم';

  @override
  String welcomeMessage(Object userName) {
    return 'أهلاً بك، $userName!';
  }

  @override
  String get noSourcesAvailable => 'لا توجد مصادر متاحة لهذه القناة';

  @override
  String get liveTab => 'مباشر';

  @override
  String get fixturesTab => 'المباريات';

  @override
  String get favoritesTab => 'المفضلة';

  @override
  String get channelsTitle => 'القنوات';

  @override
  String get channelListPlaceholder => 'قائمة القنوات ستكون هنا';

  @override
  String get language_ar => 'العربية';

  @override
  String get language_en => 'الإنجليزية';

  @override
  String get language_es => 'الإسبانية';

  @override
  String get language_fr => 'الفرنسية';

  @override
  String get language_pl => 'البولندية';

  @override
  String get disclaimerTitle => 'إخلاء مسؤولية';

  @override
  String get disclaimerText =>
      'لا يستضيف هذا التطبيق أو يخزن أو يتحكم في أي تدفقات فيديو أو شعارات معروضة. يتم الحصول على جميع التدفقات خارجيًا من روابط IPTV المتاحة للجمهور والتي يحتفظ بها مشروع IPTV-org. التطبيق مخصص للاستخدام الشخصي ولأغراض إعلامية فقط. جميع الشعارات ومحتوى القناة هي ملك لأصحابها.';

  @override
  String appVersion(Object version) {
    return 'إصدار التطبيق: $version';
  }

  @override
  String get settingsTitle => 'الإعدادات';

  @override
  String get appLanguage => 'لغة التطبيق';

  @override
  String get adsEnabled => 'إظهار الإعلانات';

  @override
  String get enabled => 'مفعل';

  @override
  String get disabled => 'معطل';

  @override
  String get updatingServersData => 'تحديث بيانات الخوادم...';

  @override
  String get serversUpdateSuccess => 'تم تحديث بيانات الخوادم بنجاح!';

  @override
  String get failedToUpdateServers => 'فشل تحديث بيانات الخوادم.';

  @override
  String get serversUpdateCancelled => 'تم إلغاء تحديث بيانات الخوادم.';

  @override
  String nextServerUpdate(Object date) {
    return 'التحديث التالي للخادم: $date';
  }

  @override
  String get alreadyUpToDate => 'محدث بالفعل!';

  @override
  String get serversUpToDate => 'Servers data is up to date.';

  @override
  String iptvDataLastUpdate(Object date) {
    return 'IPTV Data Last Update: $date';
  }

  @override
  String get showFilters => 'إظهار الفلاتر';

  @override
  String get hideFilters => 'إخفاء الفلاتر';
}
