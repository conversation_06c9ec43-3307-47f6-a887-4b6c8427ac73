// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appTitle => 'Cat TV';

  @override
  String get helloWorld => 'Hello World';

  @override
  String welcomeMessage(Object userName) {
    return 'Welcome, $userName!';
  }

  @override
  String get noSourcesAvailable => 'No sources available for this channel';

  @override
  String get liveTab => 'Live';

  @override
  String get fixturesTab => 'Fixtures';

  @override
  String get favoritesTab => 'Favorites';

  @override
  String get channelsTitle => 'Channels';

  @override
  String get channelListPlaceholder => 'Channel list will be here';

  @override
  String get language_ar => 'Arabic';

  @override
  String get language_en => 'English';

  @override
  String get language_es => 'Spanish';

  @override
  String get language_fr => 'French';

  @override
  String get language_pl => 'Polish';

  @override
  String get disclaimerTitle => 'Disclaimer';

  @override
  String get disclaimerText =>
      'This app does not host, store, or control any video streams or logos shown. All streams are sourced externally from publicly available IPTV links maintained by the IPTV-org project. The app is for personal use and informational purposes only. All logos and channel content are property of their respective owners.';

  @override
  String appVersion(Object version) {
    return 'App Version: $version';
  }

  @override
  String get settingsTitle => 'Settings';

  @override
  String get appLanguage => 'App Language';

  @override
  String get adsEnabled => 'Show Ads';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get updatingServersData => 'Updating servers data...';

  @override
  String get serversUpdateSuccess => 'Servers data updated successfully!';

  @override
  String get failedToUpdateServers => 'Failed to update servers data.';

  @override
  String get serversUpdateCancelled => 'Servers data update cancelled.';

  @override
  String nextServerUpdate(Object date) {
    return 'Next server update: $date';
  }

  @override
  String get alreadyUpToDate => 'Already up to date!';

  @override
  String get serversUpToDate => 'Servers data is up to date.';

  @override
  String iptvDataLastUpdate(Object date) {
    return 'IPTV Data Last Update: $date';
  }

  @override
  String get showFilters => 'Show Filters';

  @override
  String get hideFilters => 'Hide Filters';
}
