// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Polish (`pl`).
class AppLocalizationsPl extends AppLocalizations {
  AppLocalizationsPl([String locale = 'pl']) : super(locale);

  @override
  String get appTitle => 'Telewizja Kotów';

  @override
  String get helloWorld => 'Witaj świecie';

  @override
  String welcomeMessage(Object userName) {
    return 'Witaj, $userName!';
  }

  @override
  String get noSourcesAvailable => 'Brak dostępnych źródeł dla tego kanału';

  @override
  String get liveTab => 'Na żywo';

  @override
  String get fixturesTab => 'Mecze';

  @override
  String get favoritesTab => 'Ulubione';

  @override
  String get channelsTitle => 'Kanały';

  @override
  String get channelListPlaceholder => 'Lista kanałów będzie tutaj';

  @override
  String get language_ar => '<PERSON>ki';

  @override
  String get language_en => 'Angielski';

  @override
  String get language_es => 'His<PERSON><PERSON><PERSON>';

  @override
  String get language_fr => 'Francuski';

  @override
  String get language_pl => 'Polski';

  @override
  String get disclaimerTitle => 'Zastrzeżenie';

  @override
  String get disclaimerText =>
      'Ta aplikacja nie hostuje, nie przechowuje ani nie kontroluje żadnych wyświetlanych strumieni wideo ani logo. Wszystkie strumienie pochodzą zewnętrznie z publicznie dostępnych linków IPTV utrzymywanych przez projekt IPTV-org. Aplikacja jest przeznaczona wyłącznie do użytku osobistego i informacyjnego. Wszystkie logo i zawartość kanałów są własnością ich odpowiednich właścicieli.';

  @override
  String appVersion(Object version) {
    return 'Wersja aplikacji: $version';
  }

  @override
  String get settingsTitle => 'Ustawienia';

  @override
  String get appLanguage => 'Język aplikacji';

  @override
  String get adsEnabled => 'Pokaż reklamy';

  @override
  String get enabled => 'Włączone';

  @override
  String get disabled => 'Wyłączone';

  @override
  String get updatingServersData => 'Aktualizowanie danych serwerów...';

  @override
  String get serversUpdateSuccess => 'Dane serwerów zaktualizowane pomyślnie!';

  @override
  String get failedToUpdateServers =>
      'Nie udało się zaktualizować danych serwerów.';

  @override
  String get serversUpdateCancelled =>
      'Aktualizacja danych serwerów anulowana.';

  @override
  String nextServerUpdate(Object date) {
    return 'Następna aktualizacja serwera: $date';
  }

  @override
  String get alreadyUpToDate => 'Już aktualne!';

  @override
  String get serversUpToDate => 'Servers data is up to date.';

  @override
  String iptvDataLastUpdate(Object date) {
    return 'IPTV Data Last Update: $date';
  }

  @override
  String get showFilters => 'Pokaż filtry';

  @override
  String get hideFilters => 'Ukryj filtry';
}
