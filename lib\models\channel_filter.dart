class ChannelFilter {
  final String? region;
  final String? country;
  final String? language;
  final String? name;
  final int? categoryId;

  const ChannelFilter({
    this.region,
    this.country,
    this.language,
    this.name,
    this.categoryId,
  });

  ChannelFilter copyWith({
    String? region,
    String? country,
    String? language,
    String? name,
    int? categoryId,
  }) {
    return ChannelFilter(
      region: region ?? this.region,
      country: country ?? this.country,
      language: language ?? this.language,
      name: name ?? this.name,
      categoryId: categoryId ?? this.categoryId,
    );
  }

  static const empty = ChannelFilter();
}
