import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/pages/player_page.dart';
import 'package:cat_tv/pages/webview_page.dart';
import 'package:flutter/foundation.dart'; // Import kDebugMode

import 'package:shared_preferences/shared_preferences.dart';

class AdPage extends StatefulWidget {
  final String url;
  final String refererUrl;
  final Channel? channel;
  final List<Map<String, dynamic>> channelSources; // Changed to list of maps
  final bool isExternal;

  const AdPage({
    super.key,
    required this.url,
    required this.refererUrl,
    required this.channel,
    required this.channelSources, // Updated to list of maps
    required this.isExternal,
  });

  @override
  State<AdPage> createState() => _AdPageState();
}

class _AdPageState extends State<AdPage> {
  InAppWebViewController? _webViewController;
  bool _canSkipAd = false;
  bool _isLoading = true; // New state for loading indicator
  int _countdownSeconds = 5; // Initial countdown for skipping
  Timer? _adTimer;
  Timer? _countdownTimer; // New timer for countdown
  int _adIndex = 0;
  final List<String> _adUrls = [
    'https://www.profitableratecpm.com/ciq0m1k61?key=02ad63d7eb1565221cc7f36795720bb7',
    'https://surefootedmaintenance.com/Xa2Uuj',
  ];

  @override
  void initState() {
    super.initState();
    if (kDebugMode) debugPrint('AdPage: initState called');

    _loadAdIndex();

    // Start a timer to enable the skip button after 3-5 seconds
    _adTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _canSkipAd = true;
        });
        if (kDebugMode) debugPrint('AdPage: Skip Ad enabled');
      }
    });

    // Start a countdown timer
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdownSeconds > 0) {
            _countdownSeconds--;
          } else {
            _countdownTimer?.cancel();
          }
        });
      }
    });
  }

  Future<void> _loadAdIndex() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _adIndex = (prefs.getInt('adIndex') ?? 0) % _adUrls.length;
    });
  }

  Future<void> _saveAdIndex() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('adIndex', (_adIndex + 1) % _adUrls.length);
  }

  @override
  void dispose() {
    if (kDebugMode) debugPrint('AdPage: dispose called');
    _adTimer?.cancel(); // Cancel the ad timer
    _countdownTimer?.cancel(); // Cancel the countdown timer
    if (_webViewController != null) {
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          _webViewController?.dispose();
          if (kDebugMode) debugPrint('AdPage: WebView disposed');
        } catch (e) {
          if (kDebugMode) {
            debugPrint('AdPage: Error disposing WebView: $e');
          }
          // Ignore disposal errors
        } finally {
          _webViewController = null;
        }
      });
    }
    super.dispose();
    _saveAdIndex();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) debugPrint('AdPage: build called');
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Ad Page',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        flexibleSpace: ClipRRect(
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 50, 0, 50), // Dark purple
                  Color.fromARGB(255, 20, 20, 20), // Dark grey
                ],
              ),
              border: Border(
                bottom: BorderSide(color: Colors.white10, width: 0.5),
              ),
            ),
          ),
        ),
        leading: Container(), // Remove back button
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromARGB(255, 20, 20, 20),
              Color.fromARGB(255, 50, 0, 50),
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              InAppWebView(
                initialUrlRequest: URLRequest(
                  url: WebUri(_adUrls[_adIndex]),
                  headers: {
                    'Referer': widget.refererUrl, // Critical for Adsterra
                  },
                ),
                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  mediaPlaybackRequiresUserGesture: false,
                  domStorageEnabled: true,
                  databaseEnabled: true,
                  javaScriptCanOpenWindowsAutomatically: false,
                  supportMultipleWindows: false,
                  allowsInlineMediaPlayback: true,
                  allowsBackForwardNavigationGestures: false,
                  allowsAirPlayForMediaPlayback: false,
                  allowFileAccessFromFileURLs: false,
                  allowUniversalAccessFromFileURLs: false,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
                ),
                onWebViewCreated: (controller) {
                  _webViewController = controller;
                },
                onLoadStart: (controller, url) {
                  if (kDebugMode) {
                    debugPrint('AdPage: WebView Load Start: $url');
                  }
                  setState(() {
                    _isLoading = true;
                  });
                },
                onLoadStop: (controller, url) async {
                  if (kDebugMode) {
                    debugPrint('AdPage: WebView Load Stop: $url');
                  }
                  setState(() {
                    _isLoading = false;
                  });
                },
                onReceivedError: (controller, request, error) {
                  if (kDebugMode) {
                    debugPrint('AdPage: WebView Error: ${error.description}');
                  }
                  // Handle errors
                },
                onConsoleMessage: (controller, consoleMessage) {
                  if (kDebugMode) {
                    debugPrint(
                      'AdPage: WebView Console: ${consoleMessage.message}',
                    );
                  }
                  // Log console messages
                },
              ),
              if (_isLoading) const Center(child: CircularProgressIndicator()),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 20.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!_canSkipAd)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Ad skippable in $_countdownSeconds seconds',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      else
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Ad ready to skip!',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ElevatedButton(
                        onPressed:
                            _canSkipAd
                                ? () {
                                  if (widget.isExternal) {
                                    Navigator.pushReplacement(
                                      context,
                                      PageRouteBuilder(
                                        pageBuilder:
                                            (
                                              context,
                                              animation,
                                              secondaryAnimation,
                                            ) => WebViewPage(
                                              channel: widget.channel!,
                                              channelUrl:
                                                  widget
                                                      .channelSources
                                                      .first['source_url'],
                                            ),
                                        transitionsBuilder: (
                                          context,
                                          animation,
                                          secondaryAnimation,
                                          child,
                                        ) {
                                          return FadeTransition(
                                            opacity: animation,
                                            child: child,
                                          );
                                        },
                                      ),
                                    );
                                  } else {
                                    Navigator.pushReplacement(
                                      context,
                                      PageRouteBuilder(
                                        pageBuilder:
                                            (
                                              context,
                                              animation,
                                              secondaryAnimation,
                                            ) => PlayerPage(
                                              channel: widget.channel!,
                                              channelSources:
                                                  widget
                                                      .channelSources, // Pass the list of maps
                                            ),
                                        transitionsBuilder: (
                                          context,
                                          animation,
                                          secondaryAnimation,
                                          child,
                                        ) {
                                          return FadeTransition(
                                            opacity: animation,
                                            child: child,
                                          );
                                        },
                                      ),
                                    );
                                  }
                                }
                                : null, // Disable button if not ready to skip
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              _canSkipAd
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 15,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(
                          _canSkipAd ? 'Skip Ad' : 'Please wait...',
                          style: const TextStyle(fontSize: 18),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
