import 'package:flutter/material.dart';
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/services/favorites_service.dart';
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/pages/player_page.dart';
import 'package:cat_tv/pages/webview_page.dart';
import 'package:cat_tv/widgets/channel_skeleton_loader.dart'; // Import the skeleton loader
import 'package:cat_tv/utils/display_mode.dart'; // Import the common DisplayMode enum
import 'package:cat_tv/pages/home_page.dart'; // Import HomePage
import 'package:cat_tv/widgets/channel_card_widget.dart'; // Import ChannelCardWidget

class FavoritesPage extends StatefulWidget {
  const FavoritesPage({super.key});

  @override
  State<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends State<FavoritesPage> {
  List<Channel> _channels = [];
  List<Channel> _filteredChannels = [];
  bool _isLoading = false;
  String? _error;
  late ChannelRepository _repo;
  late Database _db;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _initDbAndLoad();
    _searchController.addListener(_filterChannels);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterChannels);
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _initDbAndLoad() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      await FavoritesService.init();
      _db = await DatabaseLoader.openPrebuiltDatabase();
      _repo = ChannelRepository(_db);
      await _loadFavoriteChannels();
    } catch (e) {
      setState(() {
        _error = 'Failed to initialize: $e';
        _isLoading = false;
      });
      if (kDebugMode) {
        print('Error in _initDbAndLoad: $e');
      }
    }
  }

  Future<void> _loadFavoriteChannels() async {
    try {
      final favoriteChannels = await _repo.getFavoriteChannels();
      if (kDebugMode) {
        print('Loaded ${favoriteChannels.length} favorite channels');
      }

      setState(() {
        _channels = favoriteChannels;
        _filterChannels(); // Apply filter after loading
        _isLoading = false;
      });
    } catch (e, st) {
      if (kDebugMode) {
        print('Error loading favorite channels: $e');
        print(st);
      }
      setState(() {
        _error = 'Failed to load favorites: $e';
        _isLoading = false;
      });
    }
  }

  void _filterChannels() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredChannels =
          _channels.where((channel) {
            return channel.name.toLowerCase().contains(query) ||
                channel.countryCode.toLowerCase().contains(query);
          }).toList();
    });
  }

  Widget _buildChannelList() {
    if (_isLoading && _filteredChannels.isEmpty) {
      return const ChannelSkeletonLoader(
        displayMode: DisplayMode.list,
      ); // Use list mode for skeleton
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _initDbAndLoad,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurpleAccent,
                foregroundColor: Colors.white,
              ),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_filteredChannels.isEmpty && _searchController.text.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/logo/logo.png', // Placeholder for illustration/icon
              width: 100,
              height: 100,
            ),
            const SizedBox(height: 16),
            const Text(
              'No favorites yet! Tap ★ on channels to add them here.',
              style: TextStyle(fontSize: 18, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.pushReplacement(
                  context,
                  MaterialPageRoute(builder: (context) => const HomePage()),
                );
              },
              icon: const Icon(Icons.explore_outlined, color: Colors.white),
              label: const Text(
                'Browse Channels',
                style: TextStyle(color: Colors.white),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.deepPurpleAccent,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
            ),
          ],
        ),
      );
    } else if (_filteredChannels.isEmpty && _searchController.text.isNotEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 64, color: Colors.white70),
            SizedBox(height: 16),
            Text(
              'No matching channels found.',
              style: TextStyle(fontSize: 18, color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadFavoriteChannels,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return ListView.builder(
            padding: const EdgeInsets.all(8.0),
            itemCount: _filteredChannels.length,
            itemBuilder: (context, index) {
              final channel = _filteredChannels[index];
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: _buildChannelCard(context, channel),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildChannelCard(BuildContext context, Channel channel) {
    return ChannelCardWidget(
      channel: channel,
      mode: DisplayMode.list, // Force list mode
      onChannelTap: _handleChannelTap,
      onToggleFavorite: () async {
        await channel.toggleFavorite();
        _loadFavoriteChannels(); // Reload to reflect changes
      },
    );
  }

  Future<void> _handleChannelTap(BuildContext context, Channel channel) async {
    if (kDebugMode) {
      print('Fetching sources for channel: ${channel.channelId}');
    }
    final sources = await _repo.getChannelSources(channel.channelId);
    if (kDebugMode) {
      print('Channel sources for ${channel.channelId}: $sources');
    }
    if (!mounted) return;
    if (sources.isNotEmpty) {
      final isExternal = await _repo.isChannelSourceExternal(channel.channelId);
      if (!mounted) return;
      if (isExternal) {
        Navigator.push(
          // ignore: use_build_context_synchronously
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => WebViewPage(
                  channel: channel,
                  channelUrl: sources.first['source_url'],
                ),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      } else {
        Navigator.push(
          // ignore: use_build_context_synchronously
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) =>
                    PlayerPage(channel: channel, channelSources: sources),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      }
    } else {
      // ignore: use_build_context_synchronously
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('No sources available for this channel')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color.fromARGB(255, 20, 20, 20),
            Color.fromARGB(255, 50, 0, 50),
          ],
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (Navigator.canPop(context))
                        IconButton(
                          icon: const Icon(
                            Icons.arrow_back,
                            color: Colors.white,
                          ),
                          onPressed: () => Navigator.pop(context),
                        ),
                      Expanded(
                        child: Center(
                          child: Text(
                            'Favorites',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: _searchController,
                    style: const TextStyle(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'Search favorite channels...',
                      hintStyle: const TextStyle(color: Colors.white54),
                      prefixIcon: const Icon(Icons.search, color: Colors.white),
                      suffixIcon:
                          _searchController.text.isNotEmpty
                              ? IconButton(
                                icon: const Icon(
                                  Icons.clear,
                                  color: Colors.white,
                                ),
                                onPressed: () {
                                  _searchController.clear();
                                },
                              )
                              : null,
                      filled: true,
                      fillColor: Colors.white.withValues(alpha: 0.1),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(30),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Expanded(child: _buildChannelList()),
          ],
        ),
      ),
    );
  }
}
