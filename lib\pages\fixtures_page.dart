import 'package:flutter/material.dart';
import 'dart:io';
import 'dart:convert';
import 'dart:async'; // Import for Completer
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:cat_tv/services/webview_utils.dart'; // Import WebViewUtils
import 'package:cat_tv/services/fixture_link_service.dart';
import 'package:cat_tv/utils/logger.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';

class FixturesPage extends StatefulWidget {
  const FixturesPage({super.key});

  @override
  State<FixturesPage> createState() => _FixturesPageState();
}

class _FixturesPageState extends State<FixturesPage>
    with SingleTickerProviderStateMixin {
  final FixtureLinkService _fixtureLinkService = FixtureLinkService();
  String _statusMessage = '';
  Logger? _logger;
  final Completer<void> _loggerInitialized = Completer<void>();
  bool _isLoading = false;
  bool _isOpeningLink = false; // New state for link opening
  final Map<String, List<dynamic>> _scrapedData =
      {}; // New state to hold scraped data per sport
  final Map<String, ScrollController> _scrollControllers =
      {}; // Map to hold ScrollControllers for each sport
  final Map<String, DateTime?> _lastRefreshed =
      {}; // New state for last refreshed time per sport
  late TabController _tabController;
  final List<String> _sports = ['Football', 'Basketball', 'Tennis'];

  @override
  void initState() {
    super.initState();
    _initializeLogger();
    debugPrint('[FixturesPage] initState called.');
    _tabController = TabController(length: _sports.length, vsync: this);
    for (var sport in _sports) {
      _scrollControllers[sport] =
          ScrollController(); // Initialize a controller for each sport
      _loadScrapedData(sport); // Load data for each sport
      _loadLastRefreshed(sport); // Load last refreshed time for each sport
    }
  }

  Future<void> _initializeLogger() async {
    final logFile = await getLogFile();
    _logger = await getLogger(logFile: logFile);
    _loggerInitialized.complete();
  }

  Future<void> _exportFile(String sourcePath) async {
    await _loggerInitialized.future;
    var status = await Permission.manageExternalStorage.status;
    if (status.isDenied) {
      // You can request multiple permissions at once.
      Map<Permission, PermissionStatus> statuses =
          await [Permission.manageExternalStorage].request();
      print(
        statuses[Permission.manageExternalStorage],
      ); // it should print PermissionStatus.granted
    }

    if (await Permission.manageExternalStorage.isGranted) {
      try {
        final sourceFile = File(sourcePath);

        // Enhanced logging for debugging
        _logger?.i('=== EXPORT DEBUG INFO ===');
        _logger?.i('Attempting to export file from path: ${sourceFile.path}');
        _logger?.i('Source file absolute path: ${sourceFile.absolute.path}');

        // Check parent directory
        final parentDir = sourceFile.parent;
        final parentExists = await parentDir.exists();
        _logger?.i('Parent directory exists: $parentExists');
        _logger?.i('Parent directory path: ${parentDir.path}');

        // List files in parent directory for debugging
        if (parentExists) {
          try {
            final files = await parentDir.list().toList();
            _logger?.i(
              'Files in parent directory: ${files.map((f) => p.basename(f.path)).join(', ')}',
            );
          } catch (e) {
            _logger?.w('Could not list parent directory contents: $e');
          }
        }

        // Check file existence using both sync and async methods
        final bool fileExistsSync = sourceFile.existsSync();
        final bool fileExistsAsync = await sourceFile.exists();
        _logger?.i('File exists (sync): $fileExistsSync');
        _logger?.i('File exists (async): $fileExistsAsync');

        // Try to get file stats
        try {
          final stat = await sourceFile.stat();
          _logger?.i('File size: ${stat.size} bytes');
          _logger?.i('File modified: ${stat.modified}');
          _logger?.i('File type: ${stat.type}');
        } catch (e) {
          _logger?.w('Could not get file stats: $e');
        }

        if (!fileExistsAsync) {
          _logger?.e('File not found at: ${sourceFile.path}');

          // Try to create the log file if it's a log file export
          if (sourcePath.endsWith('app.log')) {
            _logger?.i('Attempting to create log file...');
            try {
              // Ensure parent directory exists
              if (!parentExists) {
                await parentDir.create(recursive: true);
                _logger?.i('Created parent directory: ${parentDir.path}');
              }

              // Create empty log file
              await sourceFile.create();
              await sourceFile.writeAsString('Log file created for export\n');
              _logger?.i('Created empty log file for export');

              // Verify creation
              if (await sourceFile.exists()) {
                _logger?.i('Log file successfully created');
              } else {
                _logger?.e('Log file creation failed');
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text(
                        'Could not create log file. Check app permissions.',
                      ),
                    ),
                  );
                }
                return;
              }
            } catch (createError) {
              _logger?.e('Error creating log file: $createError');
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Could not create log file: $createError'),
                  ),
                );
              }
              return;
            }
          } else {
            if (mounted) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('File not found. Please refresh data first.'),
                ),
              );
            }
            return;
          }
        }

        String? selectedDirectory =
            await FilePicker.platform.getDirectoryPath();

        if (selectedDirectory != null) {
          final fileName = p.basename(sourcePath);
          final destinationPath = p.join(selectedDirectory, fileName);

          // Read content from source file and write to destination
          final String fileContent = await sourceFile.readAsString();
          final destinationFile = File(destinationPath);
          await destinationFile.writeAsString(fileContent);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Exported to $destinationPath')),
            );
          }
          _logger?.i('Successfully exported file to $destinationPath');
          _logger?.i('Exported file size: ${fileContent.length} characters');
        } else {
          _logger?.i('Directory selection cancelled for export.');
        }
      } catch (e) {
        _logger?.e('Error exporting file: $e');
        _logger?.e('Stack trace: ${StackTrace.current}');
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(SnackBar(content: Text('Error exporting file: $e')));
        }
      }
    } else {
      _logger?.w('Storage permission denied for export.');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Storage permission denied')),
        );
      }
    }
  }

  Future<void> _loadLastRefreshed(String sportType) async {
    try {
      final cacheDir = await getApplicationCacheDirectory();
      final cachePath = p.join(cacheDir.path, 'fixtures');
      final file = File(p.join(cachePath, '${sportType.toLowerCase()}.json'));

      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final data = jsonDecode(jsonString);
        if (data['lastRefreshed'] != null) {
          setState(() {
            _lastRefreshed[sportType] = DateTime.fromMillisecondsSinceEpoch(
              data['lastRefreshed'],
            );
          });
        }
      }
    } catch (e) {
      debugPrint('Error loading last refreshed time for $sportType: $e');
    }
  }

  String _formatDateTime(DateTime? dateTime) {
    if (dateTime == null) {
      return 'Never';
    }
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} min ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} hr ago';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }

  @override
  void dispose() {
    _scrollControllers.forEach(
      (key, controller) => controller.dispose(),
    ); // Dispose all ScrollControllers
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadScrapedData(String sportType) async {
    _logger?.i('[_loadScrapedData] Loading data for $sportType...');
    setState(() {
      _statusMessage = 'Loading ${sportType.toLowerCase()} data...';
    });
    try {
      final cacheDir = await getApplicationCacheDirectory();
      final cachePath = p.join(cacheDir.path, 'fixtures');
      final file = File(p.join(cachePath, '${sportType.toLowerCase()}.json'));

      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final data = jsonDecode(jsonString);
        setState(() {
          _scrapedData[sportType] = data['fixtures'];
          _lastRefreshed[sportType] =
              data['lastRefreshed'] != null
                  ? DateTime.fromMillisecondsSinceEpoch(data['lastRefreshed'])
                  : null;
          _statusMessage = '$sportType data loaded successfully.';
        });
        _logger?.i(
          '[_loadScrapedData] Loaded ${sportType.toLowerCase()} content.',
        );
      } else {
        setState(() {
          _scrapedData[sportType] = [];
          _lastRefreshed[sportType] = null;
          _statusMessage = 'No ${sportType.toLowerCase()} data found.';
        });
        _logger?.i(
          '[_loadScrapedData] No ${sportType.toLowerCase()} data file found.',
        );
      }
    } catch (e) {
      setState(() {
        _statusMessage = 'Error loading ${sportType.toLowerCase()} data: $e';
      });
      _logger?.e(
        '[_loadScrapedData] Error loading ${sportType.toLowerCase()} data: $e',
      );
    } finally {
      // _isLoading is now managed by _openFixtureLink
      _logger?.i(
        '[_loadScrapedData] Finished loading for $sportType. isLoading: $_isLoading',
      );
    }
  }

  Future<void> _openFixtureLink(String sportType) async {
    _logger?.i(
      '[_openFixtureLink] Starting to open fixture link for $sportType',
    );

    setState(() {
      _isLoading = true;
      _isOpeningLink = true; // Keep this for the button state
      _statusMessage = 'Updating ${sportType.toLowerCase()} fixtures...';
    });

    // Update status and set loading state via FixtureLinkService
    await _fixtureLinkService.openFixtureLink(
      sportType: sportType,
      onOpening: (bool isOpening) {
        setState(() {
          _isOpeningLink = isOpening;
        });
        _logger?.i('[_openFixtureLink] onOpening: $isOpening');
      },
      onStatusUpdate: (String message) {
        setState(() {
          _statusMessage = message;
        });
        _logger?.i('[_openFixtureLink] onStatusUpdate: $message');
      },
    );

    final String scrapingUrl = _fixtureLinkService.getScrapingUrl(sportType);
    _logger?.i('[_openFixtureLink] Generated scraping URL: $scrapingUrl');

    List<dynamic>? scrapedData;
    HeadlessInAppWebView? headlessWebView;

    try {
      headlessWebView = HeadlessInAppWebView(
        initialUrlRequest: URLRequest(url: WebUri(scrapingUrl)),
        initialSettings: InAppWebViewSettings(
          javaScriptEnabled: true,
          userAgent:
              'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
          // Add other necessary settings for headless operation
        ),
        onWebViewCreated: (controller) {
          controller.addJavaScriptHandler(
            handlerName: 'scrapeHandler',
            callback: (args) {
              _logger?.i('Received scraped data from headless: $args');
              if (args.isNotEmpty && args[0] is List) {
                scrapedData = args[0] as List<dynamic>;
              }
            },
          );
        },
        onLoadStop: (controller, url) async {
          _logger?.i("Headless WebView finished loading: $url");

          for (int attempt = 1; attempt <= 3; attempt++) {
            _logger?.i("=== SCRAPING ATTEMPT #$attempt of 3 ===");
            _logger?.i("Current URL: ${await controller.getUrl()}");
            _logger?.i("Page title: ${await controller.getTitle()}");

            // Check if page is loaded
            final isLoaded = await controller.evaluateJavascript(
              source: "document.readyState === 'complete'",
            );
            _logger?.i("Page ready state complete: $isLoaded");

            // ignore: avoid_print
            print("Scraping attempt #$attempt of 3...");

            _logger?.i("Waiting for 3 seconds before scraping...");
            // ignore: avoid_print
            print("Waiting for 3 seconds before scraping...");
            await Future.delayed(const Duration(seconds: 3));
            _logger?.i("3-second delay finished. Starting scrape.");

            // Check for common elements that indicate page is ready
            try {
              final hasMatchList = await controller.evaluateJavascript(
                source: "document.querySelector('div.match-list') !== null",
              );
              _logger?.i("Match list element found: $hasMatchList");

              final itemCount = await controller.evaluateJavascript(
                source: "document.querySelectorAll('div.item-wrap').length",
              );
              _logger?.i("Item wrap elements found: $itemCount");
            } catch (e) {
              _logger?.w("Error checking page elements: $e");
            }

            // Inject ad blocking script (optional for headless, but good practice)
            await WebViewUtils.injectAdBlockingScript(controller);

            // Execute scraping script
            _logger?.i("Executing scraping script...");
            final scriptResult = await controller.evaluateJavascript(
              source: WebViewUtils.getScrapingScript(),
            );
            _logger?.i(
              "Script execution result type: ${scriptResult.runtimeType}",
            );

            if (scriptResult is List) {
              _logger?.i(
                "Script returned list with ${scriptResult.length} items",
              );
            } else if (scriptResult != null) {
              _logger?.i(
                "Script returned: ${scriptResult.toString().substring(0, 100)}...",
              );
            } else {
              _logger?.w("Script returned null");
            }

            // Wait a moment for the scrapeHandler to be called
            await Future.delayed(const Duration(seconds: 2));

            if (scrapedData != null && scrapedData!.isNotEmpty) {
              _logger?.i(
                "Scraping attempt #$attempt successful with ${scrapedData!.length} items.",
              );
              // ignore: avoid_print
              print("Scraping attempt #$attempt successful.");
              break; // Exit loop on success
            } else {
              _logger?.w(
                "Scraping attempt #$attempt failed - no data received.",
              );
              // ignore: avoid_print
              print("Scraping attempt #$attempt failed.");
              if (attempt < 3) {
                _logger?.i("Retrying in 2 seconds...");
                // ignore: avoid_print
                print("Retrying...");
                await Future.delayed(const Duration(seconds: 2));
              }
            }
          }
        },
        onReceivedError: (controller, request, error) {
          _logger?.e(
            "Headless WebView Error loading ${request.url}: ${error.description} (Code: ${error.type})",
          );
          setState(() {
            _statusMessage =
                'Error during headless scraping: ${error.description}';
          });
        },
        onConsoleMessage: (controller, consoleMessage) {
          _logger?.d("Headless WebView Console: ${consoleMessage.message}");
        },
      );

      await headlessWebView.run();
      // Wait for a short period to allow JavaScript to execute and call the handler
      await Future.delayed(const Duration(seconds: 5)); // Adjust as needed

      if (scrapedData != null) {
        _logger?.i(
          '[_openFixtureLink] Scraped data received: ${scrapedData?.length ?? 0} items',
        );
        await _saveScrapedData(sportType, scrapedData!);
        await _loadScrapedData(sportType); // Reload data for the specific sport
        setState(() {
          _statusMessage =
              'Finished updating ${sportType.toLowerCase()} fixtures.';
        });
        _logger?.i('[_openFixtureLink] Data saved and reloaded for $sportType');
      } else {
        _logger?.w(
          '[_openFixtureLink] No scraped data received from headless webview.',
        );
        setState(() {
          _statusMessage = 'Scraping failed or no data received.';
        });
      }
    } catch (e) {
      _logger?.e(
        '[_openFixtureLink] Error during headless WebView operation: $e',
      );
      setState(() {
        _statusMessage = 'Error during scraping: $e';
      });
    } finally {
      await headlessWebView?.dispose(); // Dispose the headless webview
      setState(() {
        _isLoading = false; // Ensure loading state is reset
        _isOpeningLink = false; // Ensure button loading state is reset
      });
      _logger?.i(
        '[_openFixtureLink] Finished opening fixture link for $sportType',
      );
    }
  }

  Future<void> _saveScrapedData(String sportType, List<dynamic> data) async {
    _logger?.i('[_saveScrapedData] Saving data for $sportType...');
    try {
      final cacheDir = await getApplicationCacheDirectory();
      final cachePath = p.join(cacheDir.path, 'fixtures');
      final fixturesDir = Directory(cachePath);
      if (!await fixturesDir.exists()) {
        await fixturesDir.create(recursive: true);
        _logger?.i('[_saveScrapedData] Created cache directory: $cachePath');
      }
      final file = File(p.join(cachePath, '${sportType.toLowerCase()}.json'));
      final now = DateTime.now();
      final dataToSave = {
        'fixtures': data,
        'lastRefreshed': now.millisecondsSinceEpoch,
      };
      await file.writeAsString(jsonEncode(dataToSave));
      // ignore: avoid_print
      print('Saved ${sportType.toLowerCase()} data to cache: ${file.path}');
      _logger?.i(
        '[_saveScrapedData] Saved ${sportType.toLowerCase()} data to cache.',
      );
    } catch (e) {
      _logger?.e(
        '[_saveScrapedData] Error saving ${sportType.toLowerCase()} data: $e',
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header and Button
        Container(
          decoration: const BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color.fromARGB(255, 50, 0, 50), // Dark purple
                Color.fromARGB(255, 20, 20, 20), // Dark grey
              ],
            ),
            border: Border(
              bottom: BorderSide(color: Colors.white10, width: 0.5),
            ),
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16.0,
                vertical: 12.0,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Fixtures',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 24,
                    ),
                  ),
                  Row(
                    // Wrap buttons in a Row
                    children: [
                      ElevatedButton(
                        onPressed:
                            _isOpeningLink || _isLoading
                                ? null
                                : () {
                                  _logger?.i('Blue button pressed');
                                  _openFixtureLink(
                                    _sports[_tabController.index],
                                  );
                                },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blueAccent,
                          foregroundColor: Colors.white,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          padding: const EdgeInsets.all(12),
                          minimumSize: const Size(48, 48),
                        ),
                        child:
                            _isOpeningLink
                                ? const Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        color: Colors.white,
                                        strokeWidth: 2,
                                      ),
                                    ),
                                    SizedBox(width: 8),
                                    Text(
                                      'Refreshing...',
                                      style: TextStyle(color: Colors.white),
                                    ),
                                  ],
                                )
                                : const Icon(Icons.open_in_browser),
                      ),
                      const SizedBox(width: 8),
                      PopupMenuButton<String>(
                        onSelected: (value) async {
                          if (value == 'export_logs') {
                            try {
                              // Ensure log file exists before export
                              final logFilePath = await ensureLogFileExists();
                              _exportFile(logFilePath);
                            } catch (e) {
                              _logger?.e(
                                'Error preparing log file for export: $e',
                              );
                              if (mounted) {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text(
                                      'Error preparing log file: $e',
                                    ),
                                  ),
                                );
                              }
                            }
                          } else if (value == 'export_fixtures') {
                            final cacheDir =
                                await getApplicationCacheDirectory();
                            final sport = _sports[_tabController.index];
                            final fixturePath = p.join(
                              cacheDir.path,
                              'fixtures',
                              '${sport.toLowerCase()}.json',
                            );
                            _exportFile(fixturePath);
                          } else if (value == 'debug_logs') {
                            // Add debug option to show log file info
                            final info = await getLogFileInfo();
                            _logger?.i('Log file debug info: $info');
                            if (mounted) {
                              showDialog(
                                context: context,
                                builder:
                                    (context) => AlertDialog(
                                      title: const Text('Log File Debug Info'),
                                      content: SingleChildScrollView(
                                        child: Text(
                                          info.entries
                                              .map(
                                                (e) => '${e.key}: ${e.value}',
                                              )
                                              .join('\n'),
                                        ),
                                      ),
                                      actions: [
                                        TextButton(
                                          onPressed:
                                              () => Navigator.pop(context),
                                          child: const Text('Close'),
                                        ),
                                      ],
                                    ),
                              );
                            }
                          }
                        },
                        itemBuilder:
                            (BuildContext context) => <PopupMenuEntry<String>>[
                              const PopupMenuItem<String>(
                                value: 'export_logs',
                                child: Text('Export Logs'),
                              ),
                              const PopupMenuItem<String>(
                                value: 'export_fixtures',
                                child: Text('Export Fixtures'),
                              ),
                              const PopupMenuItem<String>(
                                value: 'debug_logs',
                                child: Text('Debug Log Info'),
                              ),
                            ],
                        icon: const Icon(Icons.more_vert, color: Colors.white),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        // TabBar for sports categories
        Container(
          color: const Color.fromARGB(255, 20, 20, 20),
          child: TabBar(
            controller: _tabController,
            tabs: _sports.map((sport) => Tab(text: sport)).toList(),
            indicatorColor: Colors.blueAccent,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white70,
          ),
        ),
        // Status/Last Refreshed display
        if (!_isLoading) // Only show this if not loading
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              _lastRefreshed[_sports[_tabController.index]] != null
                  ? 'Last Refreshed: ${_formatDateTime(_lastRefreshed[_sports[_tabController.index]])}'
                  : _statusMessage.isNotEmpty
                  ? _statusMessage
                  : 'No fixtures to display. Please click on refresh button.',
              style: const TextStyle(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ),
        // Display scraped data
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children:
                _sports.map((sport) {
                  final currentScrapedData = _scrapedData[sport] ?? [];
                  final currentLastRefreshed = _lastRefreshed[sport];

                  if (_isLoading) {
                    return Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const CircularProgressIndicator(color: Colors.white),
                          const SizedBox(height: 16),
                          Text(
                            _statusMessage,
                            style: const TextStyle(color: Colors.white70),
                          ),
                        ],
                      ),
                    );
                  } else if (currentScrapedData.isEmpty) {
                    return Center(
                      child: Text(
                        _statusMessage.isNotEmpty &&
                                currentLastRefreshed == null
                            ? _statusMessage
                            : 'No fixtures to display. Please refresh.',
                        style: const TextStyle(
                          color: Colors.white54,
                          fontSize: 16,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    );
                  } else {
                    return Scrollbar(
                      controller:
                          _scrollControllers[sport], // Use the specific controller for this sport
                      thumbVisibility: true,
                      child: ListView.builder(
                        controller:
                            _scrollControllers[sport], // Use the specific controller for this sport
                        itemCount: currentScrapedData.length,
                        itemBuilder: (context, index) {
                          final competitionData = currentScrapedData[index];
                          final competitionName =
                              competitionData['competition'];
                          final competitionLogo = competitionData['logo'];
                          final games =
                              competitionData['games'] as List<dynamic>;

                          return Card(
                            margin: const EdgeInsets.symmetric(
                              horizontal: 12.0,
                              vertical: 6.0,
                            ),
                            color: const Color.fromARGB(255, 30, 30, 30),
                            elevation: 4.0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12.0),
                            ),
                            child: ExpansionTile(
                              backgroundColor: const Color.fromARGB(
                                255,
                                30,
                                30,
                                30,
                              ),
                              collapsedBackgroundColor: const Color.fromARGB(
                                255,
                                30,
                                30,
                                30,
                              ),
                              leading:
                                  competitionLogo != null &&
                                          competitionLogo != 'N/A'
                                      ? Image.network(
                                        competitionLogo,
                                        width: 32,
                                        height: 32,
                                        errorBuilder:
                                            (context, error, stackTrace) =>
                                                const Icon(
                                                  Icons.flag,
                                                  color: Colors.white54,
                                                  size: 32,
                                                ),
                                      )
                                      : const Icon(
                                        Icons.flag,
                                        color: Colors.white54,
                                        size: 32,
                                      ),
                              title: Text(
                                competitionName,
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                              iconColor: Colors.white,
                              collapsedIconColor: Colors.white70,
                              children:
                                  games.map<Widget>((game) {
                                    final gameType = game['type'] ?? 'match';
                                    final homeTeam = game['home_team'] ?? {};
                                    final awayTeam = game['away_team'] ?? {};

                                    // Check if this is a real event (like Apex Legends) or a match without team data
                                    final isRealEvent =
                                        gameType == 'event' &&
                                        (homeTeam['name'] == 'N/A' ||
                                            homeTeam['name'] == null) &&
                                        (awayTeam['name'] == 'N/A' ||
                                            awayTeam['name'] == null) &&
                                        game['title'] != null &&
                                        game['title'] != 'N/A';

                                    return InkWell(
                                      onTap: () {
                                        final gameUrl = game['url'];
                                        if (gameUrl != null &&
                                            gameUrl != 'N/A') {
                                          debugPrint(
                                            'Game URL tapped: $gameUrl',
                                          );
                                          // Removed _launchUrlInApp as per task requirements
                                          // If a new way to handle game links is needed, it should be implemented here.
                                        }
                                      },
                                      child: Container(
                                        margin: const EdgeInsets.symmetric(
                                          horizontal: 16.0,
                                          vertical: 8.0,
                                        ),
                                        padding: const EdgeInsets.all(12.0),
                                        decoration: BoxDecoration(
                                          color: const Color.fromARGB(
                                            255,
                                            35,
                                            0,
                                            35,
                                          ), // Darker purple
                                          borderRadius: BorderRadius.circular(
                                            8.0,
                                          ),
                                          border: Border.all(
                                            color: const Color.fromARGB(
                                              255,
                                              80,
                                              0,
                                              80,
                                            ), // Purple border
                                            width: 1.5,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.black.withValues(
                                                alpha: .4,
                                              ),
                                              spreadRadius: 1,
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child:
                                            isRealEvent
                                                ? _buildEventTile(game)
                                                : _buildMatchTile(game),
                                      ),
                                    );
                                  }).toList(),
                            ),
                          );
                        },
                      ),
                    );
                  }
                }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildEventTile(Map<String, dynamic> game) {
    final title = game['title'] ?? 'N/A';
    final time = game['time'] ?? 'N/A';
    final currentMinute = game['current_minute'] ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Text(
                title,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  time,
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
                if (currentMinute.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.red,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Text(
                      currentMinute,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  )
                else if (currentMinute.isEmpty && time != 'N/A')
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6.0,
                      vertical: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.blue,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Text(
                      _getCountdownText(time),
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMatchTile(Map<String, dynamic> game) {
    final time = game['time'] ?? 'N/A';
    final currentMinute = game['current_minute'] ?? '';
    final homeTeam = game['home_team'] ?? {};
    final awayTeam = game['away_team'] ?? {};

    // Check if game has started (has scores or current minute)
    final hasStarted =
        (homeTeam['score'] != null &&
            homeTeam['score'] != 'N/A' &&
            homeTeam['score'] != '') ||
        (awayTeam['score'] != null &&
            awayTeam['score'] != 'N/A' &&
            awayTeam['score'] != '') ||
        currentMinute.isNotEmpty;

    return Column(
      children: [
        // Time and status row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              time,
              style: const TextStyle(color: Colors.white70, fontSize: 14),
            ),
            if (currentMinute.isNotEmpty)
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.red,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Text(
                  currentMinute,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              )
            else if (!hasStarted && time != 'N/A')
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 6.0,
                  vertical: 2.0,
                ),
                decoration: BoxDecoration(
                  color: Colors.blue,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: Text(
                  _getCountdownText(time),
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
          ],
        ),
        const SizedBox(height: 8),
        // Teams and scores row
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Home Team
            Expanded(
              child: Column(
                children: [
                  if (homeTeam['logo'] != null && homeTeam['logo'] != 'N/A')
                    Image.network(
                      homeTeam['logo'],
                      width: 40,
                      height: 40,
                      errorBuilder:
                          (context, error, stackTrace) => const Icon(
                            Icons.sports_soccer,
                            color: Colors.white54,
                            size: 40,
                          ),
                    )
                  else
                    const Icon(
                      Icons.sports_soccer,
                      color: Colors.white54,
                      size: 40,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    homeTeam['name'] ?? 'N/A',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
            // Scores or VS
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Text(
                hasStarted
                    ? '${homeTeam['score'] ?? '0'} - ${awayTeam['score'] ?? '0'}'
                    : 'VS',
                style: TextStyle(
                  color: hasStarted ? Colors.white : Colors.orange,
                  fontWeight: FontWeight.bold,
                  fontSize: hasStarted ? 18 : 16,
                ),
              ),
            ),
            // Away Team
            Expanded(
              child: Column(
                children: [
                  if (awayTeam['logo'] != null && awayTeam['logo'] != 'N/A')
                    Image.network(
                      awayTeam['logo'],
                      width: 40,
                      height: 40,
                      errorBuilder:
                          (context, error, stackTrace) => const Icon(
                            Icons.sports_soccer,
                            color: Colors.white54,
                            size: 40,
                          ),
                    )
                  else
                    const Icon(
                      Icons.sports_soccer,
                      color: Colors.white54,
                      size: 40,
                    ),
                  const SizedBox(height: 4),
                  Text(
                    awayTeam['name'] ?? 'N/A',
                    style: const TextStyle(color: Colors.white70, fontSize: 12),
                    textAlign: TextAlign.center,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  String _getCountdownText(String gameTime) {
    if (gameTime == 'N/A' || gameTime.isEmpty) {
      return 'TBD';
    }

    try {
      // Parse the game time (assuming format like "18:00", "20:30", etc.)
      final timeParts = gameTime.split(':');
      if (timeParts.length != 2) {
        return 'TBD';
      }

      final gameHour = int.parse(timeParts[0]);
      final gameMinute = int.parse(timeParts[1]);

      // Get current time
      final now = DateTime.now();

      // Create game datetime for today
      var gameDateTime = DateTime(
        now.year,
        now.month,
        now.day,
        gameHour,
        gameMinute,
      );

      // If game time has passed today, assume it's tomorrow
      if (gameDateTime.isBefore(now)) {
        gameDateTime = gameDateTime.add(const Duration(days: 1));
      }

      final difference = gameDateTime.difference(now);

      if (difference.isNegative) {
        return 'Started';
      }

      final hours = difference.inHours;
      final minutes = difference.inMinutes % 60;

      if (hours > 0) {
        return '${hours}h ${minutes}m';
      } else if (minutes > 0) {
        return '${minutes}m';
      } else {
        return 'Starting';
      }
    } catch (e) {
      return 'TBD';
    }
  }
}
