// ignore_for_file: use_build_context_synchronously

import 'package:cat_tv/pages/player_page.dart';
import 'dart:ui'; // Required for ImageFilter
import 'package:flutter/material.dart';
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/repositories/region_repository.dart';
import 'package:cat_tv/repositories/country_repository.dart';
import 'package:cat_tv/repositories/language_repository.dart';
import 'package:cat_tv/repositories/category_repository.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;
import 'package:sqflite/sqflite.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/widgets/filter_widget.dart';
import 'package:cat_tv/widgets/tabbed_homepage.dart';
import 'package:cat_tv/controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/pages/webview_page.dart';
import 'package:cat_tv/pages/favorites_page.dart';
import 'package:cat_tv/pages/settings_page.dart';
import 'package:cat_tv/pages/ad_page.dart'; // Import AdPage
import 'package:cat_tv/l10n/app_localizations.dart';
import 'package:cat_tv/services/ad_manager.dart'; // Import AdManager
import 'package:cat_tv/pages/iptv_webview_page.dart'; // Import IptvWebViewFetcher
import 'package:cat_tv/services/iptv_data_service.dart'; // Import IptvDataService
import 'package:cat_tv/utils/mobile_scroll_behavior.dart'; // Import optimized scroll behavior
import 'dart:async'; // For Timer
import 'package:cat_tv/pages/fixtures_page.dart'; // Import FixturesPage

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  int _selectedIndex = 0;
  final List<Channel> _channels = [];
  final AdManager _adManager = AdManager(); // Initialize AdManager
  bool _isLoading = false;
  bool _hasMore = true;
  int _currentOffset = 0;
  final int _pageSize = 20;
  ChannelRepository? _repo;
  late Database _db;
  final OptimizedScrollController _scrollController =
      OptimizedScrollController();
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];
  bool _isIptvUpdating = false;
  String _iptvUpdateStatus = '';
  DateTime? _currentExpirationDate; // Use this for the actual expiration date
  final IptvDataService _iptvDataService = IptvDataService();
  Timer? _scrollDebounceTimer; // For debouncing scroll events

  @override
  void initState() {
    super.initState();
    // Initialize
    _initDbAndLoad();
    _scrollController.addListener(_onScroll);
    _filterController.addListener(() {
      _loadMoreChannels(reset: true);
    });
  }

  Future<void> _initDbAndLoad() async {
    setState(() => _isLoading = true);
    _db = await DatabaseLoader.openPrebuiltDatabase();
    _repo = ChannelRepository(_db);
    await _loadFilterData();
    await _loadMoreChannels(reset: true);
    await _loadIptvStatusAndExpiration(); // Load status and expiration date
    setState(() => _isLoading = false);

    // No automatic fetch on init based on new requirements.
    // The manual button will handle the update logic.
  }

  Future<void> _loadFilterData() async {
    final regionRepo = RegionRepository(_db);
    final countryRepo = CountryRepository(_db);
    final languageRepo = LanguageRepository(_db);
    final categoryRepo = CategoryRepository(_db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  Future<void> _loadMoreChannels({bool reset = false}) async {
    if (reset) {
      setState(() {
        _channels.clear();
        _currentOffset = 0;
        _hasMore = true;
      });
    }
    setState(() => _isLoading = true);
    if (kDebugMode) {
      print(
        'Loading channels: offset=$_currentOffset, limit=$_pageSize, filter=${_filterController.filter}',
      );
    }
    try {
      final newChannels = await _repo!.getChannelsPaged(
        limit: _pageSize,
        offset: _currentOffset,
        filter: _filterController.filter,
      );
      if (kDebugMode) print('Loaded ${newChannels.length} channels');
      setState(() {
        _channels.addAll(newChannels);
        _currentOffset += newChannels.length;
        _hasMore = newChannels.length == _pageSize;
        _isLoading = false;
      });
    } catch (e, st) {
      if (kDebugMode) {
        print('Error loading channels: $e');
        print(st);
      }
      setState(() => _isLoading = false);
    }
  }

  void _onScroll() {
    // Debounce scroll events to improve performance
    _scrollDebounceTimer?.cancel();
    _scrollDebounceTimer = Timer(const Duration(milliseconds: 100), () {
      // Use optimized scroll controller method
      if (_scrollController.isNearBottom() && !_isLoading && _hasMore) {
        _loadMoreChannels();
      }
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _scrollDebounceTimer?.cancel(); // Clean up debounce timer
    // The database is now a singleton managed by DatabaseLoader,
    // so it should not be closed here.
    super.dispose();
  }

  Future<void> _handleChannelTap(BuildContext context, Channel channel) async {
    if (kDebugMode) {
      print('Fetching sources for channel: ${channel.channelId}');
    }
    final sources = await _repo!.getChannelSources(channel.channelId);
    if (kDebugMode) {
      print('Channel sources for ${channel.channelId}: $sources');
    }
    if (sources.isNotEmpty) {
      final isExternal = await _repo!.isChannelSourceExternal(
        channel.channelId,
      );
      if (!mounted) return;

      final shouldShowAd = await _adManager.shouldShowAd();

      if (shouldShowAd) {
        await _adManager.recordAdDisplay();
        final adUrl = await _adManager.getAdUrl();
        if (!mounted) return; // Add mounted check here
        Navigator.push(
          context,
          PageRouteBuilder(
            pageBuilder:
                (context, animation, secondaryAnimation) => AdPage(
                  url: adUrl,
                  refererUrl: 'https://cat-tv.live/',
                  channel: channel,
                  channelSources: sources, // Pass the list of maps
                  isExternal: isExternal,
                ),
            transitionsBuilder: (
              context,
              animation,
              secondaryAnimation,
              child,
            ) {
              return FadeTransition(opacity: animation, child: child);
            },
          ),
        );
      } else {
        // Proceed to player or webview
        if (isExternal) {
          if (!mounted) return; // Add mounted check here
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder:
                  (context, animation, secondaryAnimation) => WebViewPage(
                    channel: channel,
                    channelUrl: sources.first['source_url'],
                  ), // Pass only the URL
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                return FadeTransition(opacity: animation, child: child);
              },
            ),
          );
        } else {
          if (!mounted) return; // Add mounted check here
          Navigator.push(
            context,
            PageRouteBuilder(
              pageBuilder:
                  (context, animation, secondaryAnimation) => PlayerPage(
                    channel: channel,
                    channelSources: sources,
                  ), // Pass the list of maps
              transitionsBuilder: (
                context,
                animation,
                secondaryAnimation,
                child,
              ) {
                return FadeTransition(opacity: animation, child: child);
              },
            ),
          );
        }
      }
    } else {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context)!.noSourcesAvailable),
        ),
      );
    }
  }

  Future<void> _loadIptvStatusAndExpiration() async {
    _currentExpirationDate = await _iptvDataService.getExpirationTime();
    setState(() {}); // Update UI if needed
  }

  Future<void> _triggerIptvUpdate(String initialStatusMessage) async {
    setState(() {
      _isIptvUpdating = true;
      _iptvUpdateStatus = initialStatusMessage;
    });

    final iptvFetcher = IptvWebViewFetcher();
    try {
      final result = await Navigator.push(
        context,
        PageRouteBuilder(
          pageBuilder:
              (context, animation, secondaryAnimation) =>
                  IptvWebViewPage(fetcher: iptvFetcher),
          opaque: false, // Make the route opaque to hide the content
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(opacity: animation, child: child);
          },
        ),
      );

      if (result != null && result is Map<String, dynamic>) {
        if (result.containsKey("error")) {
          setState(() {
            _iptvUpdateStatus =
                AppLocalizations.of(context)!.failedToUpdateServers;
          });
          if (kDebugMode) {
            print('IPTV Data extraction failed: ${result["error"]}');
          }
        } else {
          // Successfully updated, reload last update date
          await _loadIptvStatusAndExpiration(); // Corrected method call
          setState(() {
            _iptvUpdateStatus =
                AppLocalizations.of(context)!.serversUpdateSuccess;
          });
          if (kDebugMode) {
            print('IPTV Data extraction successful.');
          }
        }
      } else {
        setState(() {
          _iptvUpdateStatus =
              AppLocalizations.of(context)!.serversUpdateCancelled;
        });
        if (kDebugMode) {
          print('IPTV Data extraction cancelled or failed.');
        }
      }
    } catch (e) {
      setState(() {
        _iptvUpdateStatus = AppLocalizations.of(context)!.failedToUpdateServers;
      });
      if (kDebugMode) {
        print('Error during IPTV data fetching: $e');
      }
    } finally {
      // Keep the status message visible for a short duration
      await Future.delayed(const Duration(seconds: 3));
      setState(() {
        _isIptvUpdating = false;
        _iptvUpdateStatus = '';
      });
    }
  }

  void _showTemporaryStatus(String message) {
    setState(() {
      _isIptvUpdating = true;
      _iptvUpdateStatus = message;
    });
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        setState(() {
          _isIptvUpdating = false;
          _iptvUpdateStatus = '';
        });
      }
    });
  }

  List<Widget> _getWidgetOptions() {
    return <Widget>[
      Column(
        children: [
          // App bar with glassmorphism effect
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 50, 0, 50), // Dark purple
                  Color.fromARGB(255, 20, 20, 20), // Dark grey
                ],
              ),
              border: Border(
                bottom: BorderSide(color: Colors.white10, width: 0.5),
              ),
            ),
            child: SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 12.0,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Cat TV',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 24,
                      ),
                    ),
                    Row(
                      children: [
                        IconButton(
                          icon: const Icon(Icons.web, color: Colors.white),
                          onPressed: () async {
                            await _loadIptvStatusAndExpiration();
                            if (_currentExpirationDate == null ||
                                DateTime.now().isAfter(
                                  _currentExpirationDate!,
                                )) {
                              await _triggerIptvUpdate(
                                AppLocalizations.of(
                                  context,
                                )!.updatingServersData,
                              );
                            } else {
                              _showTemporaryStatus(
                                AppLocalizations.of(context)!.alreadyUpToDate,
                              );
                            }
                          },
                          tooltip: 'IPTV Account Creator',
                        ),
                        IconButton(
                          icon: const Icon(Icons.settings, color: Colors.white),
                          onPressed: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => const SettingsPage(),
                              ),
                            );
                          },
                          tooltip: 'Settings',
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          // Tabbed homepage
          Expanded(
            child:
                _repo == null
                    ? const Center(child: CircularProgressIndicator())
                    : TabbedHomepage(
                      channels: _channels,
                      repository: _repo!,
                      filterController: _filterController,
                      regions: _regions,
                      countries: _countries,
                      languages: _languages,
                      categories: _categories,
                      onChannelTap: _handleChannelTap,
                      onLoadMore: () => _loadMoreChannels(),
                      isLoading: _isLoading,
                      hasMore: _hasMore,
                    ),
          ),
        ],
      ),
      const FixturesPage(), // Use the FixturesPage here
      const FavoritesPage(), // Use the FavoritesPage here
    ];
  }

  void _onItemTapped(int index) async {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final widgetOptions = _getWidgetOptions();
    return Scaffold(
      extendBodyBehindAppBar:
          true, // Extend body behind app bar for glassmorphism
      backgroundColor: Colors.black, // Dark background for the app
      body: Stack(
        children: [
          Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 20, 20, 20),
                  Color.fromARGB(255, 50, 0, 50),
                ],
              ),
            ),
            child: widgetOptions.elementAt(_selectedIndex),
          ),
          if (_isIptvUpdating)
            Container(
              color: Colors.black54, // Semi-transparent overlay
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 20),
                    Text(
                      _iptvUpdateStatus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      bottomNavigationBar: ClipRRect(
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: BottomNavigationBar(
            items: <BottomNavigationBarItem>[
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 0
                        ? const Icon(Icons.live_tv_rounded, size: 30)
                        : const Icon(Icons.live_tv_outlined, size: 28),
                label: AppLocalizations.of(context)!.liveTab,
              ),
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 1
                        ? const Icon(Icons.sports_soccer_rounded, size: 30)
                        : const Icon(Icons.sports_soccer_outlined, size: 28),
                label: AppLocalizations.of(context)!.fixturesTab,
              ),
              BottomNavigationBarItem(
                icon:
                    _selectedIndex == 2
                        ? const Icon(Icons.favorite_rounded, size: 30)
                        : const Icon(Icons.favorite_border_rounded, size: 28),
                label: AppLocalizations.of(context)!.favoritesTab,
              ),
            ],
            currentIndex: _selectedIndex,
            selectedItemColor: Colors.deepPurpleAccent,
            unselectedItemColor: Colors.white70,
            onTap: _onItemTapped,
            backgroundColor: Colors.black.withValues(alpha: 0.4),
            type: BottomNavigationBarType.fixed,
            selectedLabelStyle: const TextStyle(fontWeight: FontWeight.bold),
            unselectedLabelStyle: const TextStyle(
              fontWeight: FontWeight.normal,
            ),
          ),
        ),
      ),
    );
  }
}

class ChannelPage extends StatefulWidget {
  const ChannelPage({super.key});

  @override
  State<ChannelPage> createState() => _ChannelPageState();
}

class _ChannelPageState extends State<ChannelPage> {
  final filter_ctrl.FilterController _filterController =
      filter_ctrl.FilterController();
  List<Region> _regions = [];
  List<Country> _countries = [];
  List<Language> _languages = [];
  List<cat_model.Category> _categories = [];

  @override
  void initState() {
    super.initState();
    _initFilterData();
    _filterController.addListener(() {
      debugPrint('[ChannelPage] Filter changed: ${_filterController.filter}');
      loadChannels();
    });
  }

  Future<void> _initFilterData() async {
    final db = await DatabaseLoader.openPrebuiltDatabase();
    final regionRepo = RegionRepository(db);
    final countryRepo = CountryRepository(db);
    final languageRepo = LanguageRepository(db);
    final categoryRepo = CategoryRepository(db);
    final regions = await regionRepo.getAllRegions();
    final countries = await countryRepo.getAllCountries();
    final languages = await languageRepo.getAllLanguages();
    final categories = await categoryRepo.getAllCategories();
    setState(() {
      _regions = regions;
      _countries = countries;
      _languages = languages;
      _categories = categories.cast<cat_model.Category>();
    });
  }

  void loadChannels() {
    final filter = _filterController.filter;
    debugPrint('Loading with filter: $filter');
  }

  @override
  void dispose() {
    _filterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(AppLocalizations.of(context)!.channelsTitle)),
      body: Column(
        children: [
          FilterWidget(
            controller: _filterController,
            regions: _regions,
            countries: _countries,
            languages: _languages,
            categories: _categories,
            showFilters: true,
          ),
          Expanded(
            child: Center(
              child: Text(AppLocalizations.of(context)!.channelListPlaceholder),
            ),
          ),
        ],
      ),
    );
  }
}

class Category {
  final int id;
  final String name;
  Category({required this.id, required this.name});

  factory Category.fromMap(Map<String, dynamic> map) {
    final idValue = map['id'];
    if (idValue == null) {
      if (kDebugMode) {
        print('Warning: Category row with null id: $map');
      }
      return Category(id: -1, name: map['name'] as String? ?? 'Unknown');
    }
    return Category(
      id: idValue is int ? idValue : int.tryParse(idValue.toString()) ?? -1,
      name: map['name'] as String? ?? 'Unknown',
    );
  }
}
