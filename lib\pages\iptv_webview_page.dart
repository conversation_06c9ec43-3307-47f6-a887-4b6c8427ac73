import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/services/iptv_data_service.dart';

class IptvWebViewFetcher {
  InAppWebViewController? _webViewController;
  final String _targetUrl = 'https://freeiptv2023-d.ottc.xyz';
  Completer<Map<String, dynamic>?>? _dataCompleter;

  Future<Map<String, dynamic>?> fetchIptvData() async {
    _dataCompleter = Completer<Map<String, dynamic>?>();

    // Create a hidden InAppWebView instance
    // This requires a BuildContext, so we'll need to pass one or find a way to create a hidden overlay.
    // For now, let's assume this is called from a context where a hidden webview can be rendered.
    // A more robust solution might involve a headless webview or a separate isolate.

    // For demonstration, we'll simulate the webview interaction.
    // In a real app, you'd need to manage the lifecycle of the InAppWebView widget
    // in a way that it's not visible but still processes the web content.
    // This is a placeholder for the actual webview interaction.

    // Since InAppWebView is a widget, it needs to be part of the widget tree.
    // A common way to do this for background tasks is to use a hidden overlay or dialog.
    // However, the user explicitly asked to hide the page, implying it should not be visible.
    // The current `IptvWebViewPage` is a full-screen page.
    // To make it hidden, we can push it as a transparent dialog.

    // This class will be responsible for the logic, and the UI part will be handled by a dialog.
    // So, this class will not directly build a widget.
    // Instead, it will expose methods that can be called by a UI component (e.g., a dialog).

    // This is a conceptual outline. The actual implementation will involve
    // creating a temporary, off-screen InAppWebView or using a package that supports headless browsing.
    // Given the current tools, the most direct way to "hide" the page is to make it transparent
    // when pushed as a route, or use Offstage as initially planned.

    // Let's refactor IptvWebViewPage to be a utility that can be pushed as a transparent route.
    // The `fetchIptvData` method will be called from `HomePage`.

    return _dataCompleter?.future;
  }

  // This method will be called by the actual InAppWebView widget
  void onWebViewCreated(InAppWebViewController controller) async {
    _webViewController = controller;
    await CookieManager.instance().deleteAllCookies();
    // ignore: deprecated_member_use
    await controller.clearCache();
    if (kDebugMode) {
      print("Cleared WebView cache and cookies.");
    }
  }

  void onLoadStop(InAppWebViewController controller, WebUri? url) async {
    if (kDebugMode) {
      print("WebView finished loading: $url");
    }
    final currentUrl = url.toString();
    if (currentUrl == _targetUrl || currentUrl == '$_targetUrl/') {
      await _clickButtonWhenAvailable();
    } else if (currentUrl.startsWith('$_targetUrl/?action=view')) {
      await _extractIptvData();
    }
  }

  void onReceivedError(
    InAppWebViewController controller,
    WebResourceRequest request,
    WebResourceError error,
  ) {
    if (kDebugMode) {
      print(
        "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
      );
    }
    _dataCompleter?.completeError({"error": error.description});
  }

  Future<void> _clickButtonWhenAvailable() async {
    if (_webViewController == null) {
      _dataCompleter?.complete(null);
      return;
    }

    if (kDebugMode) {
      print("Waiting for 10 seconds before attempting to click the button...");
    }
    await Future.delayed(const Duration(seconds: 10));

    const script = '''
      (function() {
        const buttonId = 'create-btn';
        const maxAttempts = 30; // 15 seconds / 0.5 seconds per attempt
        let attempts = 0;

        const checkButton = () => {
          const button = document.getElementById(buttonId);
          if (button && !button.disabled) {
            console.log('Button found and is not disabled, attempting to click...');
            button.click();
            return true;
          } else if (attempts < maxAttempts) {
            attempts++;
            console.log('Button not yet clickable, retrying... Attempt ' + attempts);
            setTimeout(checkButton, 500);
            return false;
          } else {
            console.log('Button not found or not clickable after max attempts.');
            return false;
          }
        };
        return checkButton();
      })();
    ''';

    try {
      if (kDebugMode) {
        print(
          "Waiting for 'Create free IPTV account !' button to be clickable...",
        );
      }
      await _webViewController!.evaluateJavascript(source: script);
      if (kDebugMode) {
        print(
          "Button 'Create free IPTV account !' click script executed. Waiting for navigation...",
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error executing button click script: $e');
      }
      _dataCompleter?.completeError({"error": e.toString()});
    }
  }

  Future<void> _extractIptvData() async {
    if (_webViewController == null) {
      _dataCompleter?.complete(null);
      return;
    }

    const script = '''
      (function() {
        const serverUrl = document.getElementById("serverUrl")?.value || "";
        const username = document.getElementById("accUser")?.value || "";
        const password = document.getElementById("accPass")?.value || "";
        const m3uLink = document.getElementById("m3uLink")?.value || "";
        const activationTime = document.getElementById("accAct")?.value || "";
        const expirationTime = document.getElementById("accExp")?.value || "";

        const data = {
          "server_url": serverUrl,
          "username": username,
          "password": password,
          "activation_time": activationTime,
          "expiration_time": expirationTime
        };
        return JSON.stringify(data);
      })();
    ''';

    try {
      if (kDebugMode) {
        print("Extracting information from the page...");
      }
      final String jsonResult = await _webViewController!.evaluateJavascript(
        source: script,
      );

      // Clean the JSON string from backslashes if they are present due to escaping
      String cleanedJsonResult = jsonResult.replaceAll(r'\"', '"');
      // If the string itself is wrapped in extra quotes, remove them
      if (cleanedJsonResult.startsWith('"') &&
          cleanedJsonResult.endsWith('"')) {
        cleanedJsonResult = cleanedJsonResult.substring(
          1,
          cleanedJsonResult.length - 1,
        );
      }

      final Map<String, dynamic> extractedData = json.decode(cleanedJsonResult);

      if (kDebugMode) {
        print("Extracted Data: $extractedData");
      }

      final iptvDataService = IptvDataService();
      await iptvDataService.saveIptvData(extractedData);

      if (kDebugMode) {
        print("IPTV data saved to cache.");
      }
      _dataCompleter?.complete(extractedData);
    } catch (e) {
      if (kDebugMode) {
        print("Failed to extract elements: $e");
      }
      _dataCompleter?.completeError({"error": e.toString()});
    }
  }

  void dispose() {
    _webViewController =
        null; // Nullify the reference, but let the widget handle the actual disposal
    if (!(_dataCompleter?.isCompleted ?? false)) {
      _dataCompleter?.complete(null);
    }
  }
}

// This page will now be a wrapper that uses IptvWebViewFetcher
// and is designed to be pushed as a transparent route.
class IptvWebViewPage extends StatefulWidget {
  final IptvWebViewFetcher fetcher;

  const IptvWebViewPage({super.key, required this.fetcher});

  @override
  State<IptvWebViewPage> createState() => _IptvWebViewPageState();
}

class _IptvWebViewPageState extends State<IptvWebViewPage> {
  @override
  void initState() {
    super.initState();
    // Start the fetching process when the page is initialized
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.fetcher
          .fetchIptvData()
          .then((data) {
            if (mounted) {
              Navigator.pop(context, data);
            }
          })
          .catchError((error) {
            if (mounted) {
              Navigator.pop(context, {"error": error.toString()});
            }
          });
    });
  }

  @override
  void dispose() {
    widget.fetcher.dispose();
    if (kDebugMode) {
      print("webview disposed");
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Render the InAppWebView off-screen or with zero opacity
    return Opacity(
      opacity: 0.0, // Make it completely invisible
      child: IgnorePointer(
        ignoring: true, // Prevent any interaction
        child: InAppWebView(
          initialUrlRequest: URLRequest(url: WebUri(widget.fetcher._targetUrl)),
          initialSettings: InAppWebViewSettings(
            javaScriptEnabled: true,
            mediaPlaybackRequiresUserGesture: false,
            domStorageEnabled: true,
            databaseEnabled: true,
            javaScriptCanOpenWindowsAutomatically: false,
            supportMultipleWindows: false,
            allowsInlineMediaPlayback: true,
            allowsBackForwardNavigationGestures: false,
            allowsAirPlayForMediaPlayback: false,
            allowFileAccessFromFileURLs: false,
            allowUniversalAccessFromFileURLs: false,
            mixedContentMode: MixedContentMode.MIXED_CONTENT_NEVER_ALLOW,
          ),
          onWebViewCreated: widget.fetcher.onWebViewCreated,
          onLoadStop: widget.fetcher.onLoadStop,
          onReceivedError: widget.fetcher.onReceivedError,
          onConsoleMessage: (controller, consoleMessage) {
            if (kDebugMode) {
              print(
                "Console Message (Hidden WebView): ${consoleMessage.message}",
              );
            }
          },
        ),
      ),
    );
  }
}
