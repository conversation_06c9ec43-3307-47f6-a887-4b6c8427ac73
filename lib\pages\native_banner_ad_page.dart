import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/pages/player_page.dart';
import 'package:flutter/foundation.dart'; // Import kDebugMode

class NativeBannerAdPage extends StatefulWidget {
  final Channel channel;
  final List<Map<String, dynamic>> channelSources; // Changed to list of maps

  const NativeBannerAdPage({
    super.key,
    required this.channel,
    required this.channelSources, // Updated to list of maps
  });

  @override
  State<NativeBannerAdPage> createState() => _NativeBannerAdPageState();
}

class _NativeBannerAdPageState extends State<NativeBannerAdPage> {
  InAppWebViewController? _webViewController;
  bool _canSkipAd = false;
  bool _isLoading = true; // New state for loading indicator
  int _countdownSeconds = 5; // Initial countdown for skipping
  Timer? _adTimer;
  Timer? _countdownTimer; // New timer for countdown

  @override
  void initState() {
    super.initState();
    if (kDebugMode) debugPrint('NativeBannerAdPage: initState called');

    // Start a timer to enable the skip button after 5 seconds
    _adTimer = Timer(const Duration(seconds: 5), () {
      if (mounted) {
        setState(() {
          _canSkipAd = true;
        });
        if (kDebugMode) debugPrint('NativeBannerAdPage: Skip Ad enabled');
      }
    });

    // Start a countdown timer
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          if (_countdownSeconds > 0) {
            _countdownSeconds--;
          } else {
            _countdownTimer?.cancel();
          }
        });
      }
    });
  }

  @override
  void dispose() {
    if (kDebugMode) debugPrint('NativeBannerAdPage: dispose called');
    _adTimer?.cancel(); // Cancel the ad timer
    _countdownTimer?.cancel(); // Cancel the countdown timer
    if (_webViewController != null) {
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          _webViewController?.dispose();
          if (kDebugMode) debugPrint('NativeBannerAdPage: WebView disposed');
        } catch (e) {
          if (kDebugMode) {
            debugPrint('NativeBannerAdPage: Error disposing WebView: $e');
          }
          // Ignore disposal errors
        } finally {
          _webViewController = null;
        }
      });
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) debugPrint('NativeBannerAdPage: build called');
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Native Ad',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: false,
        flexibleSpace: ClipRRect(
          child: Container(
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Color.fromARGB(255, 50, 0, 50), // Dark purple
                  Color.fromARGB(255, 20, 20, 20), // Dark grey
                ],
              ),
              border: Border(
                bottom: BorderSide(color: Colors.white10, width: 0.5),
              ),
            ),
          ),
        ),
        leading: Container(), // Remove back button
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromARGB(255, 20, 20, 20),
              Color.fromARGB(255, 50, 0, 50),
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              InAppWebView(
                initialUrlRequest: URLRequest(
                  url: WebUri(
                    'https://media-view.netlify.app/ad_banner.html',
                  ), // Remote URL
                ),
                initialSettings: InAppWebViewSettings(
                  javaScriptEnabled: true,
                  mediaPlaybackRequiresUserGesture: false,
                  domStorageEnabled: true,
                  databaseEnabled: true,
                  javaScriptCanOpenWindowsAutomatically: true,
                  supportMultipleWindows: true,
                  allowsInlineMediaPlayback: true,
                  allowsBackForwardNavigationGestures: true,
                  allowsAirPlayForMediaPlayback: true,
                  allowFileAccessFromFileURLs: true,
                  allowUniversalAccessFromFileURLs: true,
                  mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                  userAgent:
                      "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", // Set desktop user agent
                ),
                onWebViewCreated: (controller) {
                  _webViewController = controller;
                  if (kDebugMode) {
                    debugPrint('NativeBannerAdPage: WebView created');
                  }
                },
                onLoadStart: (controller, url) {
                  if (kDebugMode) {
                    debugPrint('NativeBannerAdPage: WebView Load Start: $url');
                  }
                  setState(() {
                    _isLoading = true;
                  });
                },
                onLoadStop: (controller, url) async {
                  if (kDebugMode) {
                    debugPrint('NativeBannerAdPage: WebView Load Stop: $url');
                  }
                  setState(() {
                    _isLoading = false;
                  });

                  // Inject JavaScript to check current URL and body content
                  final String? currentUrl = await controller
                      .evaluateJavascript(source: "window.location.href;");
                  final String? bodyContent = await controller
                      .evaluateJavascript(source: "document.body.innerHTML;");
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: Current URL in WebView: $currentUrl',
                    );
                    debugPrint(
                      'NativeBannerAdPage: Body Content in WebView: $bodyContent',
                    );
                  }
                },
                onLoadResource: (controller, resource) {
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: Loading resource: ${resource.url}',
                    );
                  }
                  // Always return null to allow all resources to load normally
                  return;
                },
                onProgressChanged: (controller, progress) {
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: WebView Progress: $progress%',
                    );
                  }
                },
                onReceivedError: (controller, request, error) {
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: WebView Error: ${error.description}',
                    );
                  }
                  // Handle errors
                },
                onReceivedHttpError: (controller, request, errorResponse) {
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: WebView HTTP Error: ${errorResponse.statusCode} - ${request.url}',
                    );
                  }
                },
                onConsoleMessage: (controller, consoleMessage) {
                  if (kDebugMode) {
                    debugPrint(
                      'NativeBannerAdPage: WebView Console: ${consoleMessage.message}',
                    );
                  }
                  // Log console messages
                },
              ),
              if (_isLoading) const Center(child: CircularProgressIndicator()),
              Align(
                alignment: Alignment.bottomCenter,
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 20.0),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (!_canSkipAd)
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Ad skippable in $_countdownSeconds seconds',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        )
                      else
                        const Padding(
                          padding: EdgeInsets.only(bottom: 8.0),
                          child: Text(
                            'Ad ready to skip!',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ElevatedButton(
                        onPressed:
                            _canSkipAd
                                ? () {
                                  Navigator.pushReplacement(
                                    context,
                                    PageRouteBuilder(
                                      pageBuilder:
                                          (
                                            context,
                                            animation,
                                            secondaryAnimation,
                                          ) => PlayerPage(
                                            channel: widget.channel,
                                            channelSources:
                                                widget
                                                    .channelSources, // Pass the list of maps
                                          ),
                                      transitionsBuilder: (
                                        context,
                                        animation,
                                        secondaryAnimation,
                                        child,
                                      ) {
                                        return FadeTransition(
                                          opacity: animation,
                                          child: child,
                                        );
                                      },
                                    ),
                                  );
                                }
                                : null, // Disable button if not ready to skip
                        style: ElevatedButton.styleFrom(
                          backgroundColor:
                              _canSkipAd
                                  ? Theme.of(context).colorScheme.primary
                                  : Colors.grey,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 30,
                            vertical: 15,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: Text(
                          _canSkipAd ? 'Skip Ad' : 'Please wait...',
                          style: const TextStyle(fontSize: 18),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
