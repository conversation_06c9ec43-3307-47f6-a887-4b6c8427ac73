import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:ui'; // Required for ImageFilter
import 'package:provider/provider.dart'; // Import provider
import 'package:cat_tv/l10n/app_localizations.dart'; // Import AppLocalizations
import 'package:cat_tv/providers/locale_provider.dart'; // Import LocaleProvider
import 'package:cat_tv/services/ad_manager.dart'; // Import AdManager
import 'package:cat_tv/services/iptv_data_service.dart'; // Import IptvDataService
import 'package:intl/intl.dart'; // Import for date formatting

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _appVersion = 'Loading...';
  late Locale _selectedLocale; // Use Locale instead of String
  bool _adsEnabled = false; // State for the ad toggle
  String _iptvLastUpdateDate =
      'Not available'; // State for IPTV last update date
  String _serverStatus = 'Loading...'; // State for server status

  @override
  void initState() {
    super.initState();
    _loadAppVersion();
    _selectedLocale =
        context.read<LocaleProvider>().locale; // Initialize with current locale
    _loadAdsEnabledState(); // Load ad state
    _loadIptvStatusAndExpirationDate(); // Load IPTV status and expiration date
  }

  Future<void> _loadAppVersion() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _appVersion = packageInfo.version;
    });
  }

  Future<void> _loadAdsEnabledState() async {
    _adsEnabled = await AdManager().getAdsEnabled();
    setState(() {});
  }

  Future<void> _loadIptvStatusAndExpirationDate() async {
    final iptvDataService = IptvDataService();
    final DateTime? expirationDate = await iptvDataService.getExpirationTime();
    final String? status = await iptvDataService.getServerStatus();

    setState(() {
      if (expirationDate != null) {
        _iptvLastUpdateDate = DateFormat.yMMMd().add_jm().format(
          expirationDate.toLocal(),
        );
      } else {
        _iptvLastUpdateDate = 'Not available';
      }
      _serverStatus = status ?? 'Not available';
    });
  }

  // Helper to get localized language name
  String _getLocalizedLanguageName(BuildContext context, Locale locale) {
    final appLocalizations = AppLocalizations.of(context)!;
    switch (locale.languageCode) {
      case 'ar':
        return appLocalizations.language_ar;
      case 'en':
        return appLocalizations.language_en;
      case 'es':
        return appLocalizations.language_es;
      case 'fr':
        return appLocalizations.language_fr;
      case 'pl':
        return appLocalizations.language_pl;
      default:
        return locale.languageCode; // Fallback
    }
  }

  @override
  Widget build(BuildContext context) {
    final appLocalizations = AppLocalizations.of(context)!;
    final localeProvider = context.read<LocaleProvider>();

    // Get supported locales and map them to localized names
    final List<Locale> supportedLocales = AppLocalizations.supportedLocales;
    final List<DropdownMenuItem<Locale>> languageDropdownItems =
        supportedLocales.map((locale) {
          return DropdownMenuItem<Locale>(
            value: locale,
            child: Text(_getLocalizedLanguageName(context, locale)),
          );
        }).toList();

    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.black,
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        title: Text(
          appLocalizations.settingsTitle, // Use localized title for settings
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 24,
          ),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        centerTitle: true,
        flexibleSpace: ClipRRect(
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
            child: Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color.fromARGB(255, 50, 0, 50), // Dark purple
                    Color.fromARGB(255, 20, 20, 20), // Dark grey
                  ],
                ),
                border: Border(
                  bottom: BorderSide(color: Colors.white10, width: 0.5),
                ),
              ),
            ),
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color.fromARGB(255, 20, 20, 20),
              Color.fromARGB(255, 50, 0, 50),
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Language selection
              Text(
                appLocalizations
                    .appLanguage, // Use localized string for "App Language"
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              DropdownButton<Locale>(
                value: _selectedLocale,
                dropdownColor: Colors.black.withValues(alpha: .8),
                style: const TextStyle(color: Colors.white, fontSize: 16),
                iconEnabledColor: Colors.white,
                onChanged: (Locale? newLocale) {
                  if (newLocale != null) {
                    setState(() {
                      _selectedLocale = newLocale;
                    });
                    localeProvider.setLocale(
                      newLocale,
                    ); // Update locale via provider
                  }
                },
                items: languageDropdownItems,
              ),
              const SizedBox(height: 24),

              // Ad control
              Text(
                appLocalizations.adsEnabled, // Localized string for "Show Ads"
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              Row(
                children: [
                  Radio<bool>(
                    value: true,
                    groupValue: _adsEnabled,
                    onChanged: (bool? value) {
                      setState(() {
                        _adsEnabled = value!;
                      });
                      AdManager().setAdsEnabled(value!);
                    },
                    fillColor: WidgetStateProperty.resolveWith<Color>((
                      Set<WidgetState> states,
                    ) {
                      if (states.contains(WidgetState.selected)) {
                        return Theme.of(context).colorScheme.primary;
                      }
                      return Colors.white70;
                    }),
                  ),
                  Text(
                    appLocalizations.enabled, // Localized string for "Enabled"
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
                  Radio<bool>(
                    value: false,
                    groupValue: _adsEnabled,
                    onChanged: (bool? value) {
                      setState(() {
                        _adsEnabled = value!;
                      });
                      AdManager().setAdsEnabled(value!);
                    },
                    fillColor: WidgetStateProperty.resolveWith<Color>((
                      Set<WidgetState> states,
                    ) {
                      if (states.contains(WidgetState.selected)) {
                        return Theme.of(context).colorScheme.primary;
                      }
                      return Colors.white70;
                    }),
                  ),
                  Text(
                    appLocalizations
                        .disabled, // Localized string for "Disabled"
                    style: const TextStyle(fontSize: 16, color: Colors.white),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // IPTV Server Update Date
              Text(
                appLocalizations.iptvDataLastUpdate(
                  _iptvLastUpdateDate,
                ), // Use localized string for "IPTV Data Last Update"
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Server Status: $_serverStatus', // Display server status
                style: const TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const SizedBox(height: 24),

              // Disclaimer
              Text(
                appLocalizations
                    .disclaimerTitle, // Use localized string for "Disclaimer"
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                appLocalizations
                    .disclaimerText, // Use localized string for disclaimer text
                style: const TextStyle(fontSize: 16, color: Colors.white70),
              ),
              const SizedBox(height: 24),

              // App Version
              const Spacer(), // Pushes content to the top, app version to the bottom
              Align(
                alignment: Alignment.bottomCenter,
                child: Text(
                  appLocalizations.appVersion(
                    _appVersion,
                  ), // Use localized string for "App Version"
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
