import 'package:cat_tv/utils/logger.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Required for SystemChrome
import 'dart:ui'; // Required for ImageFilter
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/window_manager_windows.dart'; // Import for Windows window management
import 'package:cat_tv/services/webview_utils.dart'; // Import WebViewUtils
import 'package:logger/logger.dart';

class WebViewPage extends StatefulWidget {
  final Channel? channel; // Made nullable
  final String channelUrl;
  final InAppWebViewSettings? initialWebViewSettings;
  final Function(List<dynamic>)? onScraped; // New callback for scraped data

  const WebViewPage({
    super.key,
    this.channel,
    required this.channelUrl,
    this.initialWebViewSettings,
    this.onScraped, // Initialize the new callback
  });

  @override
  State<WebViewPage> createState() => _WebViewPageState();
}

class _WebViewPageState extends State<WebViewPage> {
  InAppWebViewController? _webViewController;
  late final TextEditingController _urlController;
  bool _shouldBlockAds = true;
  bool _isDisposing =
      false; // New flag to control widget rendering during disposal
  bool _isFullScreen = false; // New state for fullscreen mode
  bool _isInitialized = false; // New flag to track WebView initialization
  Logger? _logger;

  @override
  void initState() {
    super.initState();
    _urlController = TextEditingController(text: widget.channelUrl);
    _initializeLogger();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setState(() {});
    });
  }

  Future<void> _initializeLogger() async {
    _logger = await getLogger();
  }

  @override
  void dispose() {
    _logger?.i('[WebViewPage] dispose called.');
    _urlController.dispose();
    _isDisposing = true; // Set flag to true to stop rendering the WebView

    // Ensure fullscreen mode is exited when the page is disposed
    if (_isFullScreen) {
      _toggleFullscreen();
    }

    // Dispose the webview controller immediately if it exists and is initialized
    if (_isInitialized && _webViewController != null) {
      try {
        _webViewController?.dispose();
        _logger?.i('[WebViewPage] WebView controller disposed.');
      } catch (e) {
        _logger?.e('Error during WebView disposal: $e');
      } finally {
        _webViewController = null;
        _isInitialized = false;
      }
    }
    super.dispose();
  }

  void _toggleFullscreen() {
    _logger?.i("Before fullscreen toggle: ${MediaQuery.of(context).size}");

    setState(() {
      _isFullScreen = !_isFullScreen;
    });

    if (_isFullScreen) {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.immersiveSticky,
        overlays: [],
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.enterFullscreen();
      }
    } else {
      SystemChrome.setEnabledSystemUIMode(
        SystemUiMode.edgeToEdge,
        overlays: SystemUiOverlay.values,
      );
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.portraitUp,
        DeviceOrientation.portraitDown,
      ]);
      if (defaultTargetPlatform == TargetPlatform.windows) {
        WindowManagerWindows.exitFullscreen();
      }
    }

    // Add a post-frame callback to get the size after the layout has updated
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        _logger?.i("After fullscreen toggle: ${MediaQuery.of(context).size}");
      }
    });
  }

  void _loadUrl() {
    final url = _urlController.text.trim();
    if (url.isNotEmpty && _isInitialized && _webViewController != null) {
      _webViewController?.loadUrl(urlRequest: URLRequest(url: WebUri(url)));
    }
  }

  @override
  Widget build(BuildContext context) {
    return KeyboardListener(
      focusNode: FocusNode(),
      autofocus: true,
      onKeyEvent: (KeyEvent event) {
        if (event is KeyDownEvent &&
            event.logicalKey == LogicalKeyboardKey.escape) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        }
      },
      child: PopScope(
        canPop: !_isFullScreen, // Allow pop if not fullscreen
        onPopInvokedWithResult: (didPop, result) {
          if (_isFullScreen) {
            _toggleFullscreen();
          }
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          backgroundColor: Colors.black,
          appBar:
              _isFullScreen
                  ? null // Hide AppBar in fullscreen
                  : AppBar(
                    title: Text(
                      widget.channel?.name ??
                          'Web View', // Handle nullable channel
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 20,
                      ),
                    ),
                    backgroundColor: Colors.transparent,
                    elevation: 0,
                    centerTitle: false, // Changed to false to align title left
                    flexibleSpace: ClipRRect(
                      child: BackdropFilter(
                        filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                        child: Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Color.fromARGB(255, 50, 0, 50), // Dark purple
                                Color.fromARGB(255, 20, 20, 20), // Dark grey
                              ],
                            ),
                            border: Border(
                              bottom: BorderSide(
                                color: Colors.white10,
                                width: 0.5,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    leading: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () async {
                        if (_webViewController != null &&
                            await _webViewController!.canGoBack()) {
                          _webViewController!.goBack();
                        } else {
                          if (context.mounted) {
                            Navigator.of(context).pop();
                          }
                        }
                      },
                      tooltip: 'Back to Channels',
                    ),
                    actions: [
                      IconButton(
                        icon: Icon(
                          _shouldBlockAds
                              ? Icons.shield
                              : Icons.shield_outlined,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: () {
                          setState(() {
                            _shouldBlockAds = !_shouldBlockAds;
                          });
                          _loadUrl();
                        },
                        tooltip:
                            _shouldBlockAds
                                ? 'Disable Ad Blocking'
                                : 'Enable Ad Blocking',
                      ),
                      IconButton(
                        icon: Icon(
                          _isFullScreen
                              ? Icons.fullscreen_exit
                              : Icons.fullscreen,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _toggleFullscreen,
                        tooltip:
                            _isFullScreen ? 'Exit Fullscreen' : 'Fullscreen',
                      ),
                      IconButton(
                        icon: const Icon(
                          Icons.refresh,
                          color: Colors.white,
                          size:
                              28.0, // Making it slightly larger for "thick" effect
                        ),
                        onPressed: _loadUrl,
                        tooltip: 'Refresh', // Added tooltip
                      ),
                    ],
                  ),
          body: Stack(
            children: [
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Color.fromARGB(255, 20, 20, 20),
                      Color.fromARGB(255, 50, 0, 50),
                    ],
                  ),
                ),
                child:
                    _isDisposing
                        ? const SizedBox.shrink()
                        : SafeArea(
                          child: Center(
                            child: InAppWebView(
                              initialUrlRequest: URLRequest(
                                url: WebUri(widget.channelUrl),
                              ),
                              initialSettings:
                                  widget.initialWebViewSettings ??
                                  InAppWebViewSettings(
                                    javaScriptEnabled: true,
                                    mediaPlaybackRequiresUserGesture: false,
                                    domStorageEnabled: true,
                                    databaseEnabled: true,
                                    javaScriptCanOpenWindowsAutomatically:
                                        false,
                                    supportMultipleWindows: false,
                                    allowsInlineMediaPlayback: true,
                                    allowsBackForwardNavigationGestures: false,
                                    allowsAirPlayForMediaPlayback: false,
                                    allowFileAccessFromFileURLs: false,
                                    allowUniversalAccessFromFileURLs: false,
                                    mixedContentMode:
                                        MixedContentMode
                                            .MIXED_CONTENT_NEVER_ALLOW,
                                  ),
                              shouldOverrideUrlLoading: (
                                controller,
                                navigationAction,
                              ) async {
                                final url =
                                    navigationAction.request.url.toString();
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final targetUrlDomain =
                                    WebViewUtils.getBaseDomain(url);

                                // Block any cross-domain navigation that is not explicitly allowed.
                                if (currentChannelDomain.isNotEmpty &&
                                    targetUrlDomain.isNotEmpty &&
                                    currentChannelDomain != targetUrlDomain &&
                                    WebViewUtils.shouldBlockUrl(
                                      url,
                                      widget.channelUrl,
                                    )) {
                                  _logger?.w(
                                    "Blocking cross-domain navigation: $url",
                                  );
                                  return NavigationActionPolicy.CANCEL;
                                }

                                // Otherwise, proceed with existing ad blocking logic
                                if (_shouldBlockAds &&
                                    WebViewUtils.shouldBlockUrl(
                                      url,
                                      widget.channelUrl,
                                    )) {
                                  _logger?.i("Blocking ad request: $url");
                                  return NavigationActionPolicy.CANCEL;
                                }
                                return NavigationActionPolicy.ALLOW;
                              },
                              onCreateWindow: (
                                controller,
                                createWindowRequest,
                              ) async {
                                final newUrl =
                                    createWindowRequest.request.url.toString();
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final newWindowDomain =
                                    WebViewUtils.getBaseDomain(newUrl);

                                _logger?.i(
                                  "Attempting to open new window: $newUrl (Current domain: $currentChannelDomain, New window domain: $newWindowDomain)",
                                );

                                // Allow if the new window is from the same domain as the current channel
                                if (currentChannelDomain.isNotEmpty &&
                                    newWindowDomain.isNotEmpty &&
                                    currentChannelDomain == newWindowDomain) {
                                  return true; // Allow opening the new window
                                }

                                _logger?.w("Blocking popup: $newUrl");
                                return false;
                              },
                              onWebViewCreated: (controller) {
                                _webViewController = controller;
                                setState(() {
                                  _isInitialized = true;
                                });
                                controller.addJavaScriptHandler(
                                  handlerName: 'scrapeHandler',
                                  callback: (args) {
                                    _logger?.i('Received scraped data: $args');
                                    if (args.isNotEmpty && args[0] is List) {
                                      // Call the new onScraped callback and pop with data
                                      if (context.mounted) {
                                        Navigator.of(
                                          context,
                                        ).pop(args[0] as List<dynamic>);
                                      }
                                    }
                                  },
                                );
                              },
                              onLoadStart: (controller, url) {
                                _logger?.i("WebView started loading: $url");
                                final currentChannelDomain =
                                    WebViewUtils.getBaseDomain(
                                      widget.channelUrl,
                                    );
                                final targetUrlDomain =
                                    WebViewUtils.getBaseDomain(url.toString());

                                // If it's a cross-domain navigation that is not explicitly allowed, stop loading immediately.
                                if (currentChannelDomain.isNotEmpty &&
                                    targetUrlDomain.isNotEmpty &&
                                    currentChannelDomain != targetUrlDomain &&
                                    WebViewUtils.shouldBlockUrl(
                                      url.toString(),
                                      widget.channelUrl,
                                    )) {
                                  _logger?.w(
                                    "Stopping cross-domain load in onLoadStart: $url",
                                  );
                                  controller.stopLoading();
                                }
                              },
                              onLoadStop: (controller, url) async {
                                _logger?.i("WebView finished loading: $url");
                                await WebViewUtils.injectAdBlockingScript(
                                  controller,
                                );
                                // Only scrape if the onScraped callback is provided
                                if (widget.onScraped != null) {
                                  final scrapedData = await controller
                                      .evaluateJavascript(
                                        source:
                                            WebViewUtils.getScrapingScript(),
                                      ); // Call scraping function
                                  _logger?.i("Scraped data: $scrapedData");
                                  // Pass scraped data back via the handler
                                  if (scrapedData is List) {
                                    if (context.mounted) {
                                      Navigator.of(context).pop(scrapedData);
                                    }
                                  }
                                }
                              },
                              onReceivedError: (controller, request, error) {
                                _logger?.e(
                                  "Error loading ${request.url}: ${error.description} (Code: ${error.type})",
                                );
                              },
                              onConsoleMessage: (controller, consoleMessage) {
                                _logger?.d(
                                  "Console Message: ${consoleMessage.message}",
                                );
                              },
                            ),
                          ),
                        ),
              ),
              // Escape button for fullscreen mode
              Offstage(
                offstage: !_isFullScreen,
                child: Align(
                  alignment: Alignment.topLeft,
                  child: Padding(
                    padding: EdgeInsets.only(
                      top: MediaQuery.of(context).padding.top + 10,
                      left: 10,
                    ),
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: Colors.black54,
                      onPressed: _toggleFullscreen,
                      child: const Icon(
                        Icons.fullscreen_exit,
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
