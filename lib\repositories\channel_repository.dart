import 'package:sqflite/sqflite.dart';
import '../models/channel.dart';
import '../models/channel_filter.dart';
import '../services/favorites_service.dart';

class ChannelRepository {
  final Database db;

  ChannelRepository(this.db);

  // Helper method to efficiently load channels with favorite status
  Future<List<Channel>> _loadChannelsWithFavorites(
    List<Map<String, dynamic>> rows,
  ) async {
    if (rows.isEmpty) return [];

    // Get all favorite IDs once
    final favoriteIds = await FavoritesService.getFavoriteChannelIds();
    final favoriteSet = Set<String>.from(favoriteIds);

    // Create channels with favorite status set synchronously
    return rows.map((row) {
      final channelId = row['channel_id'] as String;
      return Channel.fromMapSync(
        row,
        isFavorite: favoriteSet.contains(channelId),
      );
    }).toList();
  }

  Future<List<Channel>> getAllChannels() async {
    final result = await db.query(
      'channels',
      where: 'is_active = ?',
      whereArgs: [1],
    );
    return _loadChannelsWithFavorites(result);
  }

  Future<List<Channel>> getChannelsByCategory(int categoryId) async {
    final result = await db.query(
      'channels',
      where: 'category_id = ? AND is_active = ?',
      whereArgs: [categoryId, 1],
    );
    return _loadChannelsWithFavorites(result);
  }

  Future<List<Channel>> getChannelsByGroupId(int groupId) async {
    final result = await db.query(
      'channels',
      where: 'group_id = ? AND is_active = ?',
      whereArgs: [groupId, 1],
    );
    return _loadChannelsWithFavorites(result);
  }

  Future<List<Channel>> getChannelsByCountry(String countryCode) async {
    final result = await db.query(
      'channels',
      where: 'country_code = ? AND is_active = ?',
      whereArgs: [countryCode, 1],
    );
    return _loadChannelsWithFavorites(result);
  }

  Future<List<Channel>> getChannelsByLanguage(String languageCode) async {
    // first get the country codes for the given language code from the table country_languages
    final countryCodesResult = await db.query(
      'country_languages',
      columns: ['country_code'],
      where: 'language_code = ?',
      whereArgs: [languageCode],
    );

    // extract the country codes from the result
    final countryCodes =
        countryCodesResult.map((row) => row['country_code'] as String).toList();

    if (countryCodes.isEmpty) return [];

    // now get the channels for those country codes
    final result = await db.query(
      'channels',
      where:
          'country_code IN (${List.filled(countryCodes.length, '?').join(',')}) AND is_active = ?',
      whereArgs: [...countryCodes, 1],
    );
    return _loadChannelsWithFavorites(result);
  }

  Future<List<Map<String, dynamic>>> getChannelSources(String channelId) async {
    final result = await db.rawQuery(
      '''
      SELECT source_url, provider_id
      FROM channel_sources
      WHERE channel_id = ? AND is_active = 1
      ORDER BY priority DESC
      ''',
      [channelId],
    );
    return result;
  }

  Future<bool> isChannelSourceExternal(String channelId) async {
    final result = await db.rawQuery(
      '''
      SELECT is_external
      FROM channel_sources
      WHERE channel_id = ? AND is_active = 1
      LIMIT 1
      ''',
      [channelId],
    );
    if (result.isNotEmpty) {
      return result.first['is_external'] == 1;
    }
    return false;
  }

  Future<List<Channel>> getChannelsPaged({
    int limit = 20,
    int offset = 0,
    ChannelFilter? filter,
  }) async {
    // Build where clause and args based on filter
    final whereClauses = <String>[];
    final whereArgs = <dynamic>[];
    whereClauses.add('is_active = ?');
    whereArgs.add(1);

    if (filter != null) {
      if (filter.region != null && filter.region!.isNotEmpty) {
        // Get country codes for the selected region
        final regionCountriesResult = await db.query(
          'region_countries',
          columns: ['country_code'],
          where: 'region_code = ?',
          whereArgs: [filter.region],
        );
        final regionCountryCodes =
            regionCountriesResult
                .map((row) => row['country_code'] as String)
                .toList();
        if (regionCountryCodes.isNotEmpty) {
          whereClauses.add(
            'country_code IN (${List.filled(regionCountryCodes.length, '?').join(',')})',
          );
          whereArgs.addAll(regionCountryCodes);
        } else {
          // No countries for this region, return empty
          return [];
        }
      }
      if (filter.country != null && filter.country!.isNotEmpty) {
        whereClauses.add('country_code = ?');
        whereArgs.add(filter.country);
      }
      if (filter.language != null && filter.language!.isNotEmpty) {
        // Get country codes for the language
        final countryCodesResult = await db.query(
          'country_languages',
          columns: ['country_code'],
          where: 'language_code = ?',
          whereArgs: [filter.language],
        );
        final countryCodes =
            countryCodesResult
                .map((row) => row['country_code'] as String)
                .toList();
        if (countryCodes.isNotEmpty) {
          whereClauses.add(
            'country_code IN (${List.filled(countryCodes.length, '?').join(',')})',
          );
          whereArgs.addAll(countryCodes);
        } else {
          // No countries for this language, return empty
          return [];
        }
      }
      if (filter.categoryId != null && filter.categoryId != -1) {
        whereClauses.add('category_id = ?');
        whereArgs.add(filter.categoryId);
      }
      if (filter.name != null && filter.name!.isNotEmpty) {
        whereClauses.add('(name LIKE ? OR alt_names LIKE ?)');
        whereArgs.addAll(['%${filter.name}%', '%${filter.name}%']);
      }
    }

    final result = await db.query(
      'channels',
      where: whereClauses.join(' AND '),
      whereArgs: whereArgs,
      limit: limit,
      offset: offset,
    );
    return _loadChannelsWithFavorites(result);
  }

  // Optimized method specifically for loading favorite channels
  Future<List<Channel>> getFavoriteChannels() async {
    final favoriteIds = await FavoritesService.getFavoriteChannelIds();

    if (favoriteIds.isEmpty) return [];

    final result = await db.query(
      'channels',
      where:
          'channel_id IN (${List.filled(favoriteIds.length, '?').join(',')}) AND is_active = ?',
      whereArgs: [...favoriteIds, 1],
    );

    // All these channels are favorites by definition
    return result
        .map((row) => Channel.fromMapSync(row, isFavorite: true))
        .toList();
  }
}
