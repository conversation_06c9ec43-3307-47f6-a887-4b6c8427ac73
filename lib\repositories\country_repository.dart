import 'package:sqflite/sqflite.dart';
import '../models/country.dart';

class CountryRepository {
  final Database db;
  CountryRepository(this.db);

  Future<List<Country>> getAllCountries() async {
    final result = await db.query('countries');
    return result.map((row) => Country.fromMap(row)).toList();
  }

  Future<List<Country>> getCountriesByCodes(List<String> codes) async {
    if (codes.isEmpty) return [];
    final result = await db.query(
      'countries',
      where: 'code IN (${List.filled(codes.length, '?').join(',')})',
      whereArgs: codes,
    );
    return result.map((row) => Country.fromMap(row)).toList();
  }
}
