import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/models/group.dart';

class GroupRepository {
  Future<List<Group>> getGroups() async {
    final db = await DatabaseLoader.openPrebuiltDatabase();
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT g.id, g.name, COUNT(c.channel_id) as channel_count
      FROM groups g
      LEFT JOIN channels c ON g.id = c.group_id
      GROUP BY g.id, g.name
    ''');
    return List.generate(maps.length, (i) {
      return Group.fromMap(maps[i]);
    });
  }
}
