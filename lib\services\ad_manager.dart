import 'package:shared_preferences/shared_preferences.dart';

class AdManager {
  static const String _lastAdDisplayTimeKey = 'lastAdDisplayTime';
  static const String _adIndexKey = 'adIndex';
  static const String _adsEnabledKey =
      'adsEnabled'; // New key for ad preference
  static const int _adDisplayIntervalMinutes = 3;
  static const List<String> _adUrls = [
    'https://www.profitableratecpm.com/ciq0m1k61?key=02ad63d7eb1565221cc7f36795720bb7',
    'https://surefootedmaintenance.com/Xa2Uuj',
  ];

  Future<bool> shouldShowAd() async {
    final prefs = await SharedPreferences.getInstance();
    final adsEnabled =
        prefs.getBool(_adsEnabledKey) ?? false; // Ads disabled by default

    if (!adsEnabled) {
      return false; // If ads are disabled, never show them.
    }

    final lastDisplayTimeMillis = prefs.getInt(_lastAdDisplayTimeKey);

    if (lastDisplayTimeMillis == null) {
      // No ad has been shown yet, so show it.
      return true;
    }

    final lastDisplayTime = DateTime.fromMillisecondsSinceEpoch(
      lastDisplayTimeMillis,
    );
    final now = DateTime.now();
    final difference = now.difference(lastDisplayTime);

    // Show ad if interval passed
    return difference.inMinutes >= _adDisplayIntervalMinutes;
  }

  Future<void> setAdsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_adsEnabledKey, enabled);
  }

  Future<bool> getAdsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_adsEnabledKey) ??
        false; // Default to false (disabled)
  }

  Future<String> getAdUrl() async {
    final prefs = await SharedPreferences.getInstance();
    int adIndex = prefs.getInt(_adIndexKey) ?? 0;
    return _adUrls[adIndex % _adUrls.length];
  }

  Future<void> recordAdDisplay() async {
    final prefs = await SharedPreferences.getInstance();
    final now = DateTime.now().millisecondsSinceEpoch;
    int adIndex = prefs.getInt(_adIndexKey) ?? 0;
    await prefs.setInt(_adIndexKey, (adIndex + 1) % _adUrls.length);
    await prefs.setInt(_lastAdDisplayTimeKey, now);
  }
}
