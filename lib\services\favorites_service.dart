import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:flutter/foundation.dart';

class FavoritesService {
  static const String _favoritesFileName = 'favorites.txt';
  static String? _favoritesFilePath;
  static bool _isInitialized = false;

  static Future<void> init() async {
    try {
      final directory = await getApplicationSupportDirectory();
      final cacheDir = Directory(p.join(directory.path, 'cache'));

      // Ensure cache directory exists
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      _favoritesFilePath = p.join(cacheDir.path, _favoritesFileName);
      final file = File(_favoritesFilePath!);

      // Create file if it doesn't exist
      if (!await file.exists()) {
        await file.create(recursive: true);
        await file.writeAsString(''); // Initialize with empty content
      }

      _isInitialized = true;
      if (kDebugMode) {
        print('FavoritesService initialized. File path: $_favoritesFilePath');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing FavoritesService: $e');
      }
      _isInitialized = false;
    }
  }

  static Future<String> _getFilePath() async {
    if (!_isInitialized || _favoritesFilePath == null) {
      await init();
    }
    return _favoritesFilePath!;
  }

  static Future<List<String>> getFavoriteChannelIds() async {
    try {
      final filePath = await _getFilePath();
      final file = File(filePath);

      if (!await file.exists()) {
        if (kDebugMode) {
          print('Favorites file does not exist, returning empty list');
        }
        return [];
      }

      final content = await file.readAsString();
      final ids =
          content
              .split('\n')
              .map((id) => id.trim())
              .where((id) => id.isNotEmpty)
              .toList();

      if (kDebugMode) {
        print('Loaded favorite IDs: $ids');
      }

      return ids;
    } catch (e) {
      if (kDebugMode) {
        print('Error reading favorite channel IDs: $e');
      }
      return [];
    }
  }

  static Future<void> addFavoriteChannel(String channelId) async {
    try {
      final filePath = await _getFilePath();
      final file = File(filePath);
      final currentFavorites = await getFavoriteChannelIds();

      if (!currentFavorites.contains(channelId)) {
        currentFavorites.add(channelId);
        await file.writeAsString(currentFavorites.join('\n'));

        if (kDebugMode) {
          print('Added favorite channel: $channelId');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding favorite channel: $e');
      }
    }
  }

  static Future<void> removeFavoriteChannel(String channelId) async {
    try {
      final filePath = await _getFilePath();
      final file = File(filePath);
      final currentFavorites = await getFavoriteChannelIds();
      final updatedFavorites =
          currentFavorites.where((id) => id != channelId).toList();

      await file.writeAsString(updatedFavorites.join('\n'));

      if (kDebugMode) {
        print('Removed favorite channel: $channelId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error removing favorite channel: $e');
      }
    }
  }

  static Future<bool> isFavoriteChannel(String channelId) async {
    try {
      final currentFavorites = await getFavoriteChannelIds();
      return currentFavorites.contains(channelId);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if channel is favorite: $e');
      }
      return false;
    }
  }

  // Method to clear all favorites (useful for testing)
  static Future<void> clearAllFavorites() async {
    try {
      final filePath = await _getFilePath();
      final file = File(filePath);
      await file.writeAsString('');

      if (kDebugMode) {
        print('Cleared all favorites');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing favorites: $e');
      }
    }
  }
}
