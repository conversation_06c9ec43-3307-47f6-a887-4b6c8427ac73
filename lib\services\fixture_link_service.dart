import 'package:cat_tv/utils/logger.dart';
import 'package:flutter/foundation.dart';
import 'package:cat_tv/services/site_update_service.dart';
import 'package:logger/logger.dart';

class FixtureLinkService {
  Logger? _logger;

  FixtureLinkService() {
    _initializeLogger();
  }

  Future<void> _initializeLogger() async {
    _logger = await getLogger();
  }

  Future<void> openFixtureLink({
    required String sportType,
    required ValueSetter<bool> onOpening,
    required ValueSetter<String> onStatusUpdate,
  }) async {
    onOpening(true);
    onStatusUpdate('Preparing to scrape...');
    _logger?.i('Preparing to scrape for $sportType');
  }

  String getScrapingUrl(String sportType) {
    String baseUrl = SiteUpdateService.GOOGLE_SITE;
    if (!baseUrl.endsWith('/')) {
      baseUrl += '/';
    }
    final url = '$baseUrl${sportType.toLowerCase()}.html';
    _logger?.i('Generated scraping URL: $url');
    return url;
  }
}
