import 'dart:convert';
import 'dart:io';
import 'package:cat_tv/utils/logger.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:cat_tv/services/shared_preferences_manager.dart'; // Import the new manager

class IptvDataService {
  static const String _activationTimeKey = 'flutter.iptv_activation_time';
  static const String _expirationTimeKey = 'flutter.iptv_expiration_time';
  static const String _serverStatusKey = 'flutter.server_status';

  final SharedPreferencesManager _prefsManager = SharedPreferencesManager();
  late final Future<Logger> _loggerFuture = getLogger();

  Future<void> _log(String message) async {
    final logger = await _loggerFuture;
    logger.i(message);
  }

  Future<void> saveIptvData(Map<String, dynamic> data) async {
    await _prefsManager.updateIptvData(data);
    await _prefsManager.setServerStatus('updated');
  }

  Future<Map<String, dynamic>?> loadIptvData() async {
    final data = await _prefsManager.getIptvData();
    if (data != null) {
      await _log('Successfully loaded IPTV data from SharedPreferences.');
    } else {
      await _log('Failed to load IPTV data from SharedPreferences.');
    }
    return data;
  }

  Future<void> saveFixtures(String sport, Map<String, dynamic> data) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/cache/fixtures/$sport.json');
    try {
      await file.create(recursive: true);
      await file.writeAsString(jsonEncode(data));
      await _log('Successfully saved $sport.json to cache.');
    } catch (e) {
      await _log('Failed to save $sport.json to cache: $e');
    }
  }

  Future<Map<String, dynamic>?> loadFixtures(String sport) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/cache/fixtures/$sport.json');
    try {
      if (await file.exists()) {
        final content = await file.readAsString();
        await _log('Successfully loaded $sport.json from cache.');
        return jsonDecode(content);
      } else {
        await _log('$sport.json not found in cache.');
        return null;
      }
    } catch (e) {
      await _log('Failed to load $sport.json from cache: $e');
      return null;
    }
  }

  Future<DateTime?> getActivationTime() async {
    final prefs = await SharedPreferences.getInstance();
    final String? timestampString = prefs.getString(_activationTimeKey);
    if (timestampString == null) {
      return null;
    }
    return DateTime.tryParse(timestampString);
  }

  Future<DateTime?> getExpirationTime() async {
    final prefs = await SharedPreferences.getInstance();
    final String? timestampString = prefs.getString(_expirationTimeKey);
    if (timestampString == null) {
      return null;
    }
    return DateTime.tryParse(timestampString);
  }

  Future<String?> getServerStatus() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_serverStatusKey);
  }
}
