import 'dart:convert';
import 'dart:io' show Platform;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mixpanel_flutter/mixpanel_flutter.dart';

class MixpanelService {
  static final MixpanelService _instance = MixpanelService._internal();
  factory MixpanelService() => _instance;

  MixpanelService._internal();

  static const String _projectToken = '7ac12cc6c19763fc4ab0944193e12fbe';
  Mixpanel? _mixpanel;

  Future<void> init() async {
    if (Platform.isAndroid || Platform.isIOS) {
      _mixpanel = await Mixpanel.init(
        _projectToken,
        optOutTrackingDefault: false,
        trackAutomaticEvents: false, // Added required parameter
      );
    }
  }

  Future<void> track(
    String eventName, [
    Map<String, dynamic>? properties,
  ]) async {
    if (Platform.isAndroid || Platform.isIOS) {
      _mixpanel?.track(eventName, properties: properties);
    } else {
      await _sendEventViaHttp(eventName, properties ?? {});
    }
  }

  Future<void> _sendEventViaHttp(
    String eventName,
    Map<String, dynamic> properties,
  ) async {
    final body = {
      'event': eventName,
      'properties': {'token': _projectToken, ...properties},
    };

    final res = await http.post(
      Uri.parse('https://api.mixpanel.com/track?ip=1'),
      headers: {'Content-Type': 'application/json'},
      body: json.encode([body]),
    );

    if (res.statusCode == 200) {
      debugPrint('Mixpanel event sent: $eventName');
    } else {
      debugPrint('Mixpanel error: ${res.statusCode} ${res.body}');
    }
  }
}
