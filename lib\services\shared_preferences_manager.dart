import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesManager {
  static const String _iptvDataKey = 'flutter.iptv_data';
  static const String _activationTimeKey = 'flutter.iptv_activation_time';
  static const String _expirationTimeKey = 'flutter.iptv_expiration_time';
  static const String _serverStatusKey = 'flutter.server_status';

  Future<void> updateIptvData(Map<String, dynamic> newData) async {
    final prefs = await SharedPreferences.getInstance();

    // The user's feedback indicates that the data is being double-encoded.
    // The IPTV account creator is likely already providing a JSON string,
    // which is then being encoded again when saving to SharedPreferences.
    // We should directly save the newData if it's the primary data source,
    // or ensure it's properly merged without double encoding.

    // For now, let's assume newData is the complete data from the IPTV account creator
    // and it should overwrite previous IPTV data, not merge.
    // If merging is still desired, the newData itself should be a Map, not a JSON string.

    // If newData contains a key like 'iptv_data' which is already a JSON string,
    // we need to decode it first before merging.
    Map<String, dynamic> dataToProcess = {};
    if (newData.containsKey('iptv_data') && newData['iptv_data'] is String) {
      try {
        dataToProcess =
            json.decode(newData['iptv_data']) as Map<String, dynamic>;
      } catch (e) {
        debugPrint("Error decoding 'iptv_data' string from newData: $e");
        dataToProcess = newData; // Fallback to using newData as is
      }
    } else {
      dataToProcess = newData;
    }

    // Load existing data to merge, but only if it's not a fresh overwrite from iptv_account creator
    String? existingDataJson = prefs.getString(_iptvDataKey);
    Map<String, dynamic> currentData = {};

    if (existingDataJson != null) {
      try {
        currentData = json.decode(existingDataJson) as Map<String, dynamic>;
      } catch (e) {
        debugPrint("Error decoding existing IPTV data: $e");
      }
    }

    // Merge new data with existing data, prioritizing newData
    currentData.addAll(dataToProcess);

    final String? activationTime = currentData['activation_time'] as String?;
    final String? expirationTime = currentData['expiration_time'] as String?;

    final Map<String, dynamic> dataToSave = Map.from(currentData);
    dataToSave.remove('activation_time');
    dataToSave.remove('expiration_time');

    // Ensure the JSON string is clean before saving
    String finalJsonString = json.encode(dataToSave);
    finalJsonString = finalJsonString.replaceAll(
      r'\\',
      '',
    ); // Remove escaped backslashes

    await prefs.setString(_iptvDataKey, finalJsonString);

    if (activationTime != null) {
      await prefs.setString(_activationTimeKey, activationTime);
    }
    if (expirationTime != null) {
      await prefs.setString(_expirationTimeKey, expirationTime);
    }
  }

  Future<void> deleteIptvData(List<String> keysToDelete) async {
    final prefs = await SharedPreferences.getInstance();

    String? existingDataJson = prefs.getString(_iptvDataKey);
    Map<String, dynamic> currentData = {};

    if (existingDataJson != null) {
      try {
        currentData = json.decode(existingDataJson) as Map<String, dynamic>;
      } catch (e) {
        debugPrint("Error decoding existing IPTV data for deletion: $e");
      }
    }

    for (String key in keysToDelete) {
      currentData.remove(key);
    }

    await prefs.setString(_iptvDataKey, json.encode(currentData));

    if (keysToDelete.contains('activation_time')) {
      await prefs.remove(_activationTimeKey);
    }
    if (keysToDelete.contains('expiration_time')) {
      await prefs.remove(_expirationTimeKey);
    }
  }

  Future<void> clearAllIptvData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_iptvDataKey);
    await prefs.remove(_activationTimeKey);
    await prefs.remove(_expirationTimeKey);
    await prefs.remove(_serverStatusKey);
  }

  Future<void> setServerStatus(String status) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_serverStatusKey, status);
  }

  Future<Map<String, dynamic>?> getIptvData() async {
    final prefs = await SharedPreferences.getInstance();
    final String? dataJson = prefs.getString(_iptvDataKey);
    if (dataJson == null) {
      return null;
    }
    try {
      final Map<String, dynamic> data =
          json.decode(dataJson) as Map<String, dynamic>;
      final String? activationTime = prefs.getString(_activationTimeKey);
      final String? expirationTime = prefs.getString(_expirationTimeKey);
      if (activationTime != null) {
        data['activation_time'] = activationTime;
      }
      if (expirationTime != null) {
        data['expiration_time'] = expirationTime;
      }
      return data;
    } catch (e) {
      debugPrint("Error decoding IPTV data from SharedPreferences: $e");
      return null;
    }
  }
}
