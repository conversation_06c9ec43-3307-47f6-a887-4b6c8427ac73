// import 'dart:async';
// import 'dart:io';
// import 'package:flutter/foundation.dart';
// import 'package:flutter_inappwebview/flutter_inappwebview.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:cat_tv/services/webview_utils.dart';
// import 'package:cat_tv/utils/logger.dart';

class SiteUpdateService {
  // ignore: constant_identifier_names
  static const GOOGLE_SITE = 'https://www.rbtvplus01.com/';

  // Future<String> _getRedirectedUrl(String url) async {
  //   try {
  //     final client = HttpClient();
  //     var request = await client.getUrl(Uri.parse(url));
  //     request.followRedirects = true; // Follow redirects
  //     var response = await request.close();
  //     // Get the final URL after all redirects
  //     final finalUrl =
  //         response.redirects.isNotEmpty
  //             ? response.redirects.last.location.toString()
  //             : url;
  //     client.close();
  //     return finalUrl;
  //   } catch (e) {
  //     debugPrint('Error getting redirected URL for $url: $e');
  //     return url; // Return original URL on error
  //   }
  // }

  // Future<String> updateSite() async {
  //   logger.i('Starting site update');
  //   String statusMessage = 'Fetching data...';

  //   const url = 'https://sites.google.com/view/superabbit77link2';
  //   HeadlessInAppWebView? headlessWebView;
  //   final Completer<List<String>> completer = Completer<List<String>>();

  //   try {
  //     headlessWebView = HeadlessInAppWebView(
  //       initialUrlRequest: URLRequest(url: WebUri(url)),
  //       initialSettings: InAppWebViewSettings(
  //         isInspectable: true,
  //         javaScriptCanOpenWindowsAutomatically: true,
  //         mediaPlaybackRequiresUserGesture: false,
  //         allowsInlineMediaPlayback: true,
  //         iframeAllow: "camera; microphone",
  //         iframeAllowFullscreen: true,
  //         userAgent:
  //             'Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/100.0.4896.127 Mobile Safari/537.36',
  //       ),
  //       onWebViewCreated: (controller) {
  //         debugPrint('Headless WebView for _updateSite created.');
  //         controller.addJavaScriptHandler(
  //           handlerName: 'googleSitesScrapeHandler',
  //           callback: (args) async {
  //             debugPrint('Google Sites scrape handler called with data: $args');
  //             if (args.isNotEmpty && args[0] is List) {
  //               final List<String> scraped = List<String>.from(args[0]);
  //               debugPrint('Scraped links from Google Sites: $scraped');
  //               if (!completer.isCompleted) {
  //                 completer.complete(scraped);
  //               }
  //             } else {
  //               if (!completer.isCompleted) {
  //                 completer.complete([]);
  //               }
  //             }
  //           },
  //         );
  //       },
  //       onLoadStop: (controller, url) async {
  //         debugPrint('Headless WebView for _updateSite finished loading: $url');
  //         await WebViewUtils.injectAdBlockingScript(controller);
  //         await controller.evaluateJavascript(
  //           source: WebViewUtils.getGoogleSitesScrapingScript(),
  //         );
  //         Future.delayed(const Duration(seconds: 5), () {
  //           if (!completer.isCompleted) {
  //             debugPrint(
  //               'Timeout: googleSitesScrapeHandler not called, completing with empty list.',
  //             );
  //             completer.complete([]);
  //           }
  //         });
  //       },
  //       onReceivedError: (controller, request, error) {
  //         debugPrint(
  //           'Headless WebView for _updateSite Load Error: ${request.url}, ${error.type}, ${error.description}',
  //         );
  //         statusMessage =
  //             'Failed to load page: ${error.description} (Code: ${error.type})';
  //         if (!completer.isCompleted) {
  //           completer.completeError(
  //             'Failed to load page: ${error.description} (Code: ${error.type})',
  //           );
  //         }
  //       },
  //       onConsoleMessage: (controller, consoleMessage) {
  //         if (kDebugMode) {
  //           debugPrint(
  //             'Headless WebView for _updateSite Console: ${consoleMessage.message}',
  //           );
  //         }
  //       },
  //     );

  //     debugPrint('Attempting to run headless WebView for _updateSite.');
  //     await headlessWebView.run();
  //     debugPrint('Headless WebView for _updateSite finished running.');

  //     final List<String> extractedLinks = await completer.future;
  //     debugPrint(
  //       'Completer finished. Extracted links count: ${extractedLinks.length}',
  //     );

  //     if (extractedLinks.isNotEmpty) {
  //       final linksToProcess = extractedLinks.take(2).toList();
  //       final Set<String> redirectedLinks = {};
  //       for (final link in linksToProcess) {
  //         final redirectedUrl = await _getRedirectedUrl(link);
  //         redirectedLinks.add(redirectedUrl);
  //       }

  //       final prefs = await SharedPreferences.getInstance();
  //       await prefs.setStringList('fixture_links', redirectedLinks.toList());

  //       statusMessage =
  //           'Found ${redirectedLinks.length} unique links and saved to preferences.';
  //       for (var link in redirectedLinks) {
  //         logger.i('Saved Link: $link');
  //       }
  //     } else {
  //       statusMessage = 'No links found.';
  //       logger.w('No links were extracted from Google Sites.');
  //     }
  //   } catch (e) {
  //     statusMessage = 'Error: $e';
  //     logger.e('Error during site update: $e');
  //     if (!completer.isCompleted) {
  //       completer.completeError(e);
  //     }
  //   } finally {
  //     headlessWebView?.dispose();
  //     debugPrint('Headless WebView for _updateSite disposed.');
  //   }
  //   return statusMessage;
  // }
}
