import 'package:flutter/foundation.dart';
import 'package:cat_tv/services/iptv_data_service.dart';

class StreamUrlBuilderService {
  final IptvDataService _iptvDataService;
  Map<String, dynamic>? _cachedIptvData; // Add a cache for IPTV data

  StreamUrlBuilderService(this._iptvDataService);

  /// Builds the complete stream URL for provider_id = 2.
  /// The base URL is constructed from decrypted IPTV data,
  /// and the sourceId is appended with a .m3u8 extension.
  Future<String?> buildProvider2StreamUrl(String sourceId) async {
    // Load IPTV data from cache first, then from service if not available
    _cachedIptvData ??= await _iptvDataService.loadIptvData();

    if (_cachedIptvData == null) {
      debugPrint("Failed to load IPTV data for stream URL construction.");
      return null;
    }

    try {
      final String? serverUrl = _cachedIptvData!['server_url'];
      final String? username = _cachedIptvData!['username'];
      final String? password = _cachedIptvData!['password'];

      if (serverUrl == null || username == null || password == null) {
        debugPrint(
          "Missing required IPTV data components (server_url, username, password) for stream URL construction.",
        );
        return null;
      }

      // Parse the serverUrl to extract host and port
      Uri uri = Uri.parse(serverUrl);
      final String host = uri.host;
      final int httpPort = uri.port;
      final int httpsPort = 443; // Default HTTPS port

      // Construct the base HTTP URL
      final String httpBaseUrl =
          "http://$host:$httpPort/live/$username/$password/";

      // Convert to HTTPS URL
      final String httpsBaseUrl =
          "https://$host:$httpsPort/live/$username/$password/";

      // Print the constructed base URLs for debugging
      debugPrint("Constructed HTTP Base URL: $httpBaseUrl");
      debugPrint("Constructed HTTPS Base URL: $httpsBaseUrl");

      // Append the sourceId with .m3u8 extension
      // .m3u8 is generally preferred for MediaKit as it supports HLS adaptive streaming.
      final String finalStreamUrl = "$httpsBaseUrl$sourceId.m3u8";

      return finalStreamUrl;
    } catch (e) {
      debugPrint("Error building provider 2 stream URL: $e");
      return null;
    }
  }
}
