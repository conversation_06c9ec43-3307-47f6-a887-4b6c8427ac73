import 'package:cat_tv/utils/logger.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'dart:io';
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as p;
import 'package:logger/logger.dart';

class WebViewUtils {
  static Logger? _logger;

  static Future<void> _initializeLogger() async {
    _logger = await getLogger();
  }

  // Enhanced ad blocking patterns including aggressive redirects and downloads
  static final List<String> _adBlockPatterns = [
    // Common ad networks
    'doubleclick.net',
    'googleadservices.com',
    'googlesyndication.com',
    'googletagmanager.com',
    'google-analytics.com',
    'facebook.com/tr',
    'amazon-adsystem.com',
    'adsystem.amazon.com',
    'ads.yahoo.com',
    'advertising.com',
    'adsystem.com',
    'adsense.com',
    'adnxs.com',
    'ads.twitter.com',
    'analytics.twitter.com',
    'ads.linkedin.com',
    'ads.pinterest.com',
    'ads.reddit.com',
    'outbrain.com',
    'taboola.com',
    'scorecardresearch.com',
    'quantserve.com',

    // Aggressive redirect and download patterns
    'operagx.gg',
    'opera.com',
    'operagx.com',
    'download.opera.com',
    'get.opera.com',
    'install.opera.com',
    'setup.opera.com',
    'redirect.opera.com',
    'cdn.opera.com',
    'operacdn.com',
    'operasoftware.com',
    'opera-api.com',

    // Common redirect domains
    'bit.ly',
    'tinyurl.com',
    'short.link',
    'redirect.link',
    'go.link',
    'click.link',
    'track.link',
    'affiliate.link',
    'promo.link',
    'offer.link',

    // Download and installer patterns
    'download.',
    'installer.',
    'setup.',
    'install.',
    'get.',
    'fetch.',
    'grab.',
    'dl.',
    '.exe',
    '.msi',
    '.dmg',
    '.pkg',
    '.deb',
    '.rpm',

    // Generic ad patterns
    'adsystem',
    'advertising',
    'googleads',
    'adsense',
    'adservice',
    'adserver',
    'adnetwork',
    'adnxs',
    'ads.',
    '/ads/',
    'advertisement',
    'popup',
    'popunder',
    'interstitial',
    'overlay',
    'banner',
    'sponsored',
    'promo',
    'affiliate',
    'referral',
    'tracking',
    'analytics',
    'metrics',
    'telemetry',
  ];

  // Allowlist for video player and streaming domains
  static final List<String> _allowedDomains = [
    'jwpcdn.com',
    'jwplatform.com',
    'jwplayer.com',
    'videojs.com',
    'vimeo.com',
    'youtube.com',
    'youtu.be',
    'dailymotion.com',
    'twitch.tv',
    'hls.js',
    'dash.js',
    'shaka-player',
    'plyr.io',
    'flowplayer.com',
    'brightcove.com',
    'kaltura.com',
    'wistia.com',
    'vidyard.com',
    'cloudflare.com',
    'amazonaws.com',
    'azure.com',
    'googleapis.com',
    'gstatic.com',
    'jsdelivr.net',
    'unpkg.com',
    'cdnjs.cloudflare.com',
    'profitableratecpm.com', // Whitelist Adsterra domain
    'morgan.h3eaulperhapsfuzkhurried.shop', // Whitelist for cross-domain player
  ];

  static String getBaseDomain(String url) {
    try {
      final uri = Uri.parse(url);
      // Return host, removing 'www.' if present
      return uri.host.startsWith('www.') ? uri.host.substring(4) : uri.host;
    } catch (e) {
      _logger?.e('Error parsing URL for domain: $url, Error: $e');
      return ''; // Return empty string on error
    }
  }

  static bool shouldBlockUrl(String url, String currentChannelUrl) {
    final lowerUrl = url.toLowerCase();

    // Get the base domain of the current channel URL
    final currentChannelDomain = getBaseDomain(currentChannelUrl);
    // Get the base domain of the URL being navigated to
    final targetUrlDomain = getBaseDomain(url);

    // Allow navigation if the domains are the same or if the target URL is in the allowed domains
    if (currentChannelDomain.isNotEmpty &&
        targetUrlDomain.isNotEmpty &&
        currentChannelDomain == targetUrlDomain) {
      return false;
    }

    // Don't block allowed video player domains
    if (_allowedDomains.any((domain) => lowerUrl.contains(domain))) {
      return false;
    }

    // Check against our pattern list
    if (_adBlockPatterns.any((pattern) => lowerUrl.contains(pattern))) {
      return true;
    }

    // Additional checks for aggressive redirects
    if (_isAggressiveRedirect(lowerUrl)) {
      return true;
    }

    // Block download attempts
    if (_isDownloadAttempt(lowerUrl)) {
      return true;
    }

    return false;
  }

  static bool _isAggressiveRedirect(String url) {
    // Block URLs that look like aggressive redirects
    final redirectPatterns = [
      RegExp(r'redirect\..*'),
      RegExp(r'.*\/redirect\/.*'),
      RegExp(r'.*\/go\/.*'),
      RegExp(r'.*\/click\/.*'),
      RegExp(r'.*\/track\/.*'),
      RegExp(r'.*\/out\/.*'),
      RegExp(r'.*\/exit\/.*'),
      RegExp(r'.*\/link\/.*'),
      RegExp(r'.*\/promo\/.*'),
      RegExp(r'.*\/offer\/.*'),
      RegExp(r'.*\/download\/.*'),
      RegExp(r'.*\/install\/.*'),
      RegExp(r'.*\/setup\/.*'),
      RegExp(r'.*\/get\/.*'),
    ];

    return redirectPatterns.any((pattern) => pattern.hasMatch(url));
  }

  static bool _isDownloadAttempt(String url) {
    // Block direct download attempts
    final downloadExtensions = [
      '.exe',
      '.msi',
      '.dmg',
      '.pkg',
      '.deb',
      '.rpm',
      '.zip',
      '.rar',
      '.7z',
      '.tar.gz',
      '.tar.bz2',
      '.apk',
      '.ipa',
      '.app',
      '.bin',
      '.run',
    ];

    return downloadExtensions.any((ext) => url.endsWith(ext));
  }

  static Future<void> injectAdBlockingScript(
    InAppWebViewController controller,
  ) async {
    const script = '''
      (function() {
        console.log('Ad blocking script injected');

        // Block aggressive redirects
        const originalOpen = window.open;
        window.open = function(url, name, specs) {
          console.log('Blocked window.open attempt:', url);
          return null;
        };

        // Block location changes
        const originalAssign = window.location.assign;
        const originalReplace = window.location.replace;
        const originalReload = window.location.reload;

        window.location.assign = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.assign:', url);
            return;
          }
          return originalAssign.call(this, url);
        };

        window.location.replace = function(url) {
          if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
            console.log('Blocked location.replace:', url);
            return;
          }
          return originalReplace.call(this, url);
        };

        // Block document.location changes (only if not already defined)
        try {
          let originalLocation = document.location;
          Object.defineProperty(document, 'location', {
            get: function() { return originalLocation; },
            set: function(url) {
              if (url && (url.includes('opera') || url.includes('download') || url.includes('install'))) {
                console.log('Blocked document.location change:', url);
                return;
              }
              originalLocation = url;
            },
            configurable: true
          });
        } catch (e) {
          console.log('Could not redefine document.location (already defined):', e.message);
        }

        // Block aggressive click handlers (but preserve player controls)
        document.addEventListener('click', function(e) {
          const target = e.target;
          const href = target.href || target.getAttribute('href');

          // Check if this is a player control element
          const isPlayerControl = target.closest('video') ||
                                target.closest('[class*="player"]') ||
                                target.closest('[class*="video"]') ||
                                target.closest('[class*="control"]') ||
                                target.closest('[class*="quality"]') ||
                                target.closest('[class*="menu"]') ||
                                target.closest('[class*="button"]') ||
                                target.closest('[role="button"]') ||
                                target.hasAttribute('aria-label') ||
                                target.tagName === 'BUTTON';

          if (!isPlayerControl && href && (href.includes('opera') || href.includes('download') || href.includes('install'))) {
            console.log('Blocked click on suspicious link:', href);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Block form submissions to suspicious URLs
        document.addEventListener('submit', function(e) {
          const action = e.target.action;
          if (action && (action.includes('opera') || action.includes('download') || action.includes('install'))) {
            console.log('Blocked form submission to:', action);
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
        }, true);

        // Remove suspicious elements (but preserve video player controls)
        function removeSuspiciousElements() {
          const suspiciousSelectors = [
            'a[href*="opera"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="download"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'a[href*="install"]:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            'iframe[src*="opera"]:not([class*="player"]):not([class*="video"])',
            'iframe[src*="download"]:not([class*="player"]):not([class*="video"])',
            'iframe[src*="install"]:not([class*="player"]):not([class*="video"])',
            '.popup:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])',
            '.interstitial:not([class*="player"]):not([class*="control"]):not([class*="video"])',
            '[id*="popup"]:not([id*="player"]):not([id*="control"]):not([id*="video"]):not([id*="quality"]):not([id*="menu"])',
            '[class*="popup"]:not([class*="player"]):not([class*="control"]):not([class*="video"]):not([class*="quality"]):not([class*="menu"])'
          ];

          suspiciousSelectors.forEach(selector => {
            try {
              const elements = document.querySelectorAll(selector);
              elements.forEach(el => {
                // Additional check to avoid removing video player elements
                const isPlayerElement = el.closest('video') ||
                                      el.closest('[class*="player"]') ||
                                      el.closest('[class*="video"]') ||
                                      el.closest('[class*="control"]') ||
                                      el.closest('[class*="quality"]') ||
                                      el.closest('[class*="menu"]') ||
                                      el.querySelector('video') ||
                                      el.querySelector('[class*="player"]') ||
                                      el.querySelector('[class*="video"]');

                if (!isPlayerElement) {
                  console.log('Removing suspicious element:', el);
                  el.remove();
                }
              });
            } catch (e) {
              // Ignore errors
            }
          });
        }

        // Run immediately and on DOM changes
        removeSuspiciousElements();

        // Watch for new elements
        const observer = new MutationObserver(function(mutations) {
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
              removeSuspiciousElements();
            }
          });
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true
        });

        console.log('Ad blocking script fully loaded');
      })();
    ''';

    try {
      await controller.evaluateJavascript(source: script);
    } catch (e) {
      _logger?.e('Error evaluating ad blocking script: $e');
    }
  }

  static String getScrapingScript() {
    const script = '''
      (function() {
        const result = [];
        const competitionsMap = new Map(); // To group games by competition
        const matchListDiv = document.querySelector('.match-list');

        if (matchListDiv) {
          const itemWraps = matchListDiv.querySelectorAll('.item-wrap');

          itemWraps.forEach((itemWrap, index) => {

            // Get the first anchor tag (competition info)
            const competitionAnchor = itemWrap.querySelector('a._6xejLK');
            if (!competitionAnchor) {
              return;
            }

            // Get competition details
            const competitionDiv = competitionAnchor.querySelector('.gJrz5O');
            if (!competitionDiv) {
              return;
            }

            // Get country and competition name spans
            const countrySpan = competitionDiv.querySelector('span:first-of-type');
            const competitionNameSpan = competitionDiv.querySelector('span.Bld2gZ');

            if (!competitionNameSpan) {
              return;
            }

            const competitionName = competitionNameSpan.textContent.trim();

            // Check if country span has display:none style (skip condition)
            const countryText = countrySpan ? countrySpan.textContent.trim() : '';
            const isCountryHidden = countrySpan && countrySpan.style.display === 'none';

            if (isCountryHidden) {
              return;
            }

            // Get competition logo
            const competitionLogo = competitionDiv.querySelector('img');
            const competitionLogoUrl = competitionLogo ? (competitionLogo.getAttribute('src') || competitionLogo.getAttribute('origin-src')) : null;

            // Get the second anchor tag (game info)
            const gameAnchor = itemWrap.querySelector('a.kAx4Cs');
            if (!gameAnchor) {
              return;
            }

            const gameUrl = gameAnchor.getAttribute('href');

            // Get game time
            const timeDiv = gameAnchor.querySelector('.RIPS\\\\+p');
            let gameTime = 'N/A';
            let currentMinute = '';

            if (timeDiv) {
              const timeSpan = timeDiv.querySelector('span:not(.s6v7bH):not(.uSpUtY)');
              if (timeSpan) {
                gameTime = timeSpan.textContent.trim();
              }

              // Get current minute if game is live
              const minuteSpan = timeDiv.querySelector('.kbCW\\\\+R span:not([style*="display: none"])');
              if (minuteSpan && minuteSpan.textContent.trim()) {
                currentMinute = minuteSpan.textContent.trim();
              }
            }

            // Check if this is a single event (like Apex Legends) or a match
            const matchDiv = gameAnchor.querySelector('.Rrze9z');
            let gameData = {};

            // Determine if it's a "single event" by checking for the presence of a specific title class
            // AND the absence of typical team name spans.
            const isSingleEvent = matchDiv &&
                                  matchDiv.classList.contains('rEvTCt') &&
                                  matchDiv.querySelector('._3PYXPF') && // Specific title class for events
                                  !matchDiv.querySelector('.T5er3y ._2u2Xtc'); // No team name span in home team div

            if (isSingleEvent) {
              // Single event (like Apex Legends)
              const eventTitle = matchDiv.querySelector('._3PYXPF');
              gameData = {
                type: 'event',
                title: eventTitle ? eventTitle.textContent.trim() : 'N/A',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: { name: 'N/A', score: 'N/A', logo: null },
                away_team: { name: 'N/A', score: 'N/A', logo: null }
              };
            } else if (matchDiv) {
              // Regular match with teams (either started or unstarted)
              const homeTeamDiv = matchDiv.querySelector('.T5er3y');
              const awayTeamDiv = matchDiv.querySelector('.Ld7BaR');
              const scoresContainer = matchDiv.querySelector('._8NFEJw');

              let homeTeamName = 'N/A', homeTeamLogo = null, homeScore = 'N/A';
              let awayTeamName = 'N/A', awayTeamLogo = null, awayScore = 'N/A';

              // Extract home team info
              if (homeTeamDiv) {
                const homeNameSpan = homeTeamDiv.querySelector('._2u2Xtc');
                const homeLogoImg = homeTeamDiv.querySelector('.r-logo');
                homeTeamName = homeNameSpan ? homeNameSpan.textContent.trim() : 'N/A';
                homeTeamLogo = homeLogoImg ? (homeLogoImg.getAttribute('src') || homeLogoImg.getAttribute('origin-src')) : null;
              }

              // Extract away team info
              if (awayTeamDiv) {
                const awayNameSpan = awayTeamDiv.querySelector('._2u2Xtc');
                const awayLogoImg = awayTeamDiv.querySelector('.r-logo');
                awayTeamName = awayNameSpan ? awayNameSpan.textContent.trim() : 'N/A';
                awayTeamLogo = awayLogoImg ? (awayLogoImg.getAttribute('src') || awayLogoImg.getAttribute('origin-src')) : null;
              }

              // Check for scores - they might be hidden for games that haven't started
              if (scoresContainer) {
                const scoresDiv = scoresContainer.querySelector('.Eu8BT4');
                const vsDiv = scoresContainer.querySelector('.B106Pj');

                // If there's a visible scores div, extract scores
                if (scoresDiv && scoresDiv.style.display !== 'none') {
                  const scoreSpans = scoresDiv.querySelectorAll('.FGXWhu .bw19CK');
                  if (scoreSpans.length >= 2) {
                    const homeScoreText = scoreSpans[0].textContent.trim();
                    const awayScoreText = scoreSpans[1].textContent.trim();
                    // Only use scores if they're not empty
                    if (homeScoreText && awayScoreText) {
                      homeScore = homeScoreText;
                      awayScore = awayScoreText;
                    }
                  }
                } else if (vsDiv && vsDiv.textContent.trim() === 'VS') {
                  // Game hasn't started yet - scores remain 'N/A'
                }
              }

              // If team names are still N/A, try to extract from URL
              if ((homeTeamName === 'N/A' || awayTeamName === 'N/A') && gameUrl && gameUrl.includes('-vs-')) {
                const urlParts = gameUrl.split('/').pop().replace('.html', '');
                const teamParts = urlParts.split('-vs-');
                if (teamParts.length === 2) {
                  let homeTeamPart = teamParts[0];
                  let awayTeamPart = teamParts[1];

                  // Remove numeric IDs
                  homeTeamPart = homeTeamPart.replace(/-[0-9]+\$/, '');
                  awayTeamPart = awayTeamPart.replace(/-[0-9]+\$/, '');

                  if (homeTeamName === 'N/A') {
                    homeTeamName = homeTeamPart.split('-').map(word => {
                      if (word.toLowerCase() === 'fc') return 'FC';
                      if (word.toLowerCase() === 'w') return '(W)';
                      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }).join(' ');
                  }

                  if (awayTeamName === 'N/A') {
                    awayTeamName = awayTeamPart.split('-').map(word => {
                      if (word.toLowerCase() === 'fc') return 'FC';
                      if (word.toLowerCase() === 'w') return '(W)';
                      return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                    }).join(' ');
                  }

                }
              }

              gameData = {
                type: 'match',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: {
                  name: homeTeamName,
                  score: homeScore,
                  logo: homeTeamLogo
                },
                away_team: {
                  name: awayTeamName,
                  score: awayScore,
                  logo: awayTeamLogo
                }
              };
            } else {
              // No match div found, but we still have a game URL - try to extract team names from URL or create a basic match structure

              // Try to extract team names from the URL (usually in format: team1-vs-team2.html)
              let homeTeamName = 'N/A', awayTeamName = 'N/A';
              if (gameUrl && gameUrl.includes('-vs-')) {
                const urlParts = gameUrl.split('/').pop().replace('.html', '');
                const teamParts = urlParts.split('-vs-');
                if (teamParts.length === 2) {
                  // Clean up team names by removing competition/league identifiers and formatting properly
                  let homeTeamPart = teamParts[0];
                  let awayTeamPart = teamParts[1];

                  // Remove common prefixes that are competition identifiers
                  const competitionPrefixes = ['norwegian-eliteserien', 'finnish-veikkausliiga', 'belarusian-cup', 'international-club-friendly'];
                  competitionPrefixes.forEach(prefix => {
                    if (homeTeamPart.startsWith(prefix + '-')) {
                      homeTeamPart = homeTeamPart.substring(prefix.length + 1);
                    }
                  });

                  // Remove numeric IDs (like -4112103)
                  homeTeamPart = homeTeamPart.replace(/-[0-9]+\$/, '');
                  awayTeamPart = awayTeamPart.replace(/-[0-9]+\$/, '');

                  // Convert to proper case
                  homeTeamName = homeTeamPart.split('-').map(word => {
                    // Handle special cases
                    if (word.toLowerCase() === 'fc') return 'FC';
                    if (word.toLowerCase() === 'vs') return 'vs';
                    if (word.toLowerCase() === 'w') return '(W)';
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                  }).join(' ');

                  awayTeamName = awayTeamPart.split('-').map(word => {
                    // Handle special cases
                    if (word.toLowerCase() === 'fc') return 'FC';
                    if (word.toLowerCase() === 'vs') return 'vs';
                    if (word.toLowerCase() === 'w') return '(W)';
                    return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
                  }).join(' ');

                }
              }

              gameData = {
                type: 'match',
                time: gameTime,
                current_minute: currentMinute,
                url: gameUrl,
                home_team: {
                  name: homeTeamName,
                  score: 'N/A',
                  logo: null
                },
                away_team: {
                  name: awayTeamName,
                  score: 'N/A',
                  logo: null
                }
              };
            }

            // Group games by competition
            if (!competitionsMap.has(competitionName)) {
              competitionsMap.set(competitionName, {
                competition: competitionName,
                country: countryText.replace(':&nbsp;', '').trim(),
                logo: competitionLogoUrl,
                games: []
              });
            }

            competitionsMap.get(competitionName).games.push(gameData);
          });
        }

        // Convert map to array
        competitionsMap.forEach((value) => {
          result.push(value);
        });

        // Send the scraped data back to Flutter
        window.flutter_inappwebview.callHandler('scrapeHandler', result);
        return result;
      })();
    ''';
    return script;
  }

  static String getGoogleSitesScrapingScript() {
    const script = '''
      (function() {
        console.log('Google Sites scraping script started.');
        const extractedLinks = [];

        // Option 1: Look for divs with data-tooltip="🔗Link - something"
        // Then get the href of the a element inside.
        const linkDivs = document.querySelectorAll('div[data-tooltip^="🔗Link -"]');
        console.log('Found linkDivs:', linkDivs.length);
        for (const div of linkDivs) {
          const anchor = div.querySelector('a');
          if (anchor && anchor.hasAttribute('href')) {
            let href = anchor.getAttribute('href');
            if (href) {
              // Decode the Google URL redirect if present
              if (href.startsWith('https://www.google.com/url?q=')) {
                const urlParams = new URLSearchParams(href.split('?')[1]);
                href = urlParams.get('q');
              }
              if (href && !extractedLinks.includes(href)) {
                extractedLinks.push(href);
              }
            }
          }
        }

        // Option 2 (simpler): Look directly for anchors 'a' that have in their href 'https://www.google.com/url?q='
        const directAnchors = document.querySelectorAll('a[href^="https://www.google.com/url?q="]');
        console.log('Found directAnchors:', directAnchors.length);
        for (const anchor of directAnchors) {
          let href = anchor.getAttribute('href');
          if (href) {
            const urlParams = new URLSearchParams(href.split('?')[1]);
            href = urlParams.get('q');
            if (href && !extractedLinks.includes(href)) {
              extractedLinks.push(href);
            }
          }
        }

        console.log('Final extracted Google Sites links:', extractedLinks);
        window.flutter_inappwebview.callHandler('googleSitesScrapeHandler', extractedLinks);
        return extractedLinks;
      })();
    ''';
    return script;
  }

  static Future<void> saveScrapedData(List<dynamic> data) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cachePath = p.join(directory.path, 'cache');
      final file = File(p.join(cachePath, 'fixtures.json'));

      // Create the directory if it doesn't exist
      final cacheDir = Directory(cachePath);
      if (!await cacheDir.exists()) {
        await cacheDir.create(recursive: true);
      }

      final jsonString = jsonEncode(data);
      await file.writeAsString(jsonString);
      _logger?.i('Scraped data saved to ${file.path}');
    } catch (e) {
      _logger?.e('Error saving scraped data: $e');
    }
  }
}
