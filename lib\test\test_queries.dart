// add main here this file is used to test the methodes on channel repository only in console
import 'package:cat_tv/db/database_loader.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:flutter/widgets.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  databaseFactory = databaseFactoryFfi;
  final db = await DatabaseLoader.openPrebuiltDatabase();
  final repo = ChannelRepository(db);

  // Test getChannelsPaged
  final pagedChannels = await repo.getChannelsPaged(limit: 10, offset: 0);
  if (kDebugMode) {
    print('Paged Channels: $pagedChannels');
  }

  // test a query with a where clause
  final channelSources = await repo.getChannelSources('002RadioTV.do');
  if (kDebugMode) {
    print('Channel Sources: $channelSources');
  }
}
