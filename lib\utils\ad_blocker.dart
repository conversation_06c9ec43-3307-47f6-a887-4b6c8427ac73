import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter/widgets.dart';

class AdBlocker {
  static List<String> _blockedHosts = [];

  static Future<void> loadEasylist() async {
    if (_blockedHosts.isNotEmpty) {
      return;
    }
    try {
      final String easylistContent = await rootBundle.loadString(
        'assets/ads/easylist.txt',
      );
      _blockedHosts =
          easylistContent
              .split('\n')
              .where(
                (line) =>
                    line.isNotEmpty &&
                    !line.startsWith('!') &&
                    !line.startsWith('['),
              )
              .toList();
    } catch (e) {
      debugPrint('Error loading easylist: $e');
    }
  }

  static bool isBlocked(String url) {
    return _blockedHosts.any((host) => url.contains(host));
  }
}
