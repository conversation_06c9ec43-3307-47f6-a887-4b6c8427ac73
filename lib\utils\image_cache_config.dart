import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class ImageCacheConfig {
  static void configure() {
    // Configure global image cache settings for better performance
    // Set cache configuration
    PaintingBinding.instance.imageCache.maximumSize =
        200; // Increase cache size
    PaintingBinding.instance.imageCache.maximumSizeBytes =
        50 * 1024 * 1024; // 50MB
  }

  /// Optimized CachedNetworkImage widget with performance settings
  static Widget buildOptimizedImage({
    required String imageUrl,
    required double width,
    required double height,
    BoxFit fit = BoxFit.contain,
    Widget? placeholder,
    Widget? errorWidget,
  }) {
    return CachedNetworkImage(
      imageUrl: imageUrl,
      width: width,
      height: height,
      fit: fit,
      // Performance optimizations
      memCacheWidth: (width * 2).round(), // 2x for high DPI
      memCacheHeight: (height * 2).round(),
      maxWidthDiskCache: (width * 3).round(), // 3x for disk cache
      maxHeightDiskCache: (height * 3).round(),
      // Faster loading
      fadeInDuration: const Duration(milliseconds: 200),
      fadeOutDuration: const Duration(milliseconds: 100),
      // Error handling
      placeholder:
          (context, url) => placeholder ?? _defaultPlaceholder(width, height),
      errorWidget:
          (context, url, error) =>
              errorWidget ?? _defaultErrorWidget(width, height),
      // Cache configuration
      cacheKey: _generateCacheKey(imageUrl, width, height),
    );
  }

  static Widget _defaultPlaceholder(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[800],
      child: Icon(Icons.tv, size: width * 0.6, color: Colors.grey[600]),
    );
  }

  static Widget _defaultErrorWidget(double width, double height) {
    return Container(
      width: width,
      height: height,
      color: Colors.grey[800],
      child: Icon(
        Icons.image_not_supported,
        size: width * 0.6,
        color: Colors.grey[600],
      ),
    );
  }

  static String _generateCacheKey(String url, double width, double height) {
    return '${url}_${width.round()}x${height.round()}';
  }

  /// Preload images for better scrolling performance
  static void preloadImage(
    String imageUrl,
    BuildContext context, {
    double? width,
    double? height,
  }) {
    if (imageUrl.isEmpty) return;

    try {
      precacheImage(
        CachedNetworkImageProvider(
          imageUrl,
          cacheKey:
              width != null && height != null
                  ? _generateCacheKey(imageUrl, width, height)
                  : null,
        ),
        context,
      );
    } catch (e) {
      // Silently handle preload errors
      debugPrint('Failed to preload image: $imageUrl');
    }
  }
}
