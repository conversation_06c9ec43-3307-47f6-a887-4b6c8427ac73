import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

// Global logger instance to ensure consistency across the app
Logger? _globalLogger;

class FileOutput extends LogOutput {
  final File file;
  final bool clearExisting;
  bool _isInitialized = false;

  FileOutput(this.file, {this.clearExisting = false});

  Future<void> _ensureFileExists() async {
    if (_isInitialized) return;

    try {
      // Ensure parent directory exists
      final parentDir = file.parent;
      if (!await parentDir.exists()) {
        await parentDir.create(recursive: true);
        print('Created log directory: ${parentDir.path}');
      }

      // Clear existing file if requested
      if (clearExisting && await file.exists()) {
        await file.delete();
        print('Cleared existing log file: ${file.path}');
      }

      // Create file if it doesn't exist
      if (!await file.exists()) {
        await file.create();
        print('Created log file: ${file.path}');
      }

      _isInitialized = true;
    } catch (e) {
      print('Error initializing log file: $e');
      // Don't set _isInitialized to true if there was an error
    }
  }

  @override
  void output(OutputEvent event) {
    // Try to initialize file if not already done (synchronously for immediate logging)
    if (!_isInitialized) {
      try {
        // Synchronous initialization for immediate logging
        final parentDir = file.parent;
        if (!parentDir.existsSync()) {
          parentDir.createSync(recursive: true);
          debugPrint('Created log directory: ${parentDir.path}');
        }

        if (!file.existsSync()) {
          file.createSync();
          debugPrint('Created log file: ${file.path}');
        }

        _isInitialized = true;
      } catch (e) {
        debugPrint('Error initializing log file synchronously: $e');
      }
    }

    for (var line in event.lines) {
      try {
        // Force immediate write with flush for production reliability
        file.writeAsStringSync('$line\n', mode: FileMode.append, flush: true);

        // Also output to console in debug mode for immediate feedback
        if (kDebugMode) {
          debugPrint(line);
        }
      } catch (e) {
        // More detailed error logging
        debugPrint('Error writing to log file ${file.path}: $e');

        // Try to recreate the file if it was deleted
        if (e.toString().contains('No such file or directory') ||
            e.toString().contains('cannot find the file')) {
          try {
            _isInitialized = false;
            final parentDir = file.parent;
            if (!parentDir.existsSync()) {
              parentDir.createSync(recursive: true);
            }
            file.createSync();
            file.writeAsStringSync(
              '$line\n',
              mode: FileMode.append,
              flush: true,
            );
            _isInitialized = true;
            debugPrint('Recreated log file and wrote message');
          } catch (recreateError) {
            debugPrint('Failed to recreate log file: $recreateError');
          }
        }
      }
    }
  }
}

Future<File> getLogFile() async {
  try {
    final cacheDir = await getApplicationCacheDirectory();
    final logFile = File('${cacheDir.path}/app.log');

    // Ensure the cache directory exists
    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
      print('Created cache directory: ${cacheDir.path}');
    }

    print('Log file path: ${logFile.path}');
    return logFile;
  } catch (e) {
    print('Error getting log file path: $e');
    // Fallback to a temporary directory if cache directory fails
    try {
      final tempDir = await getTemporaryDirectory();
      final fallbackLogFile = File('${tempDir.path}/app_fallback.log');
      print('Using fallback log file: ${fallbackLogFile.path}');
      return fallbackLogFile;
    } catch (fallbackError) {
      print('Error getting fallback log file: $fallbackError');
      rethrow;
    }
  }
}

Future<Logger> getLogger({File? logFile}) async {
  // Return existing global logger if available
  if (_globalLogger != null) {
    return _globalLogger!;
  }

  try {
    final file = logFile ?? await getLogFile();

    debugPrint('Initializing logger with file: ${file.path}');

    // Check if file is accessible
    try {
      final exists = await file.exists();
      debugPrint('Log file exists: $exists');

      if (exists) {
        final stat = await file.stat();
        debugPrint('Log file size: ${stat.size} bytes');
      }
    } catch (e) {
      debugPrint('Error checking log file status: $e');
    }

    // Added for debugging production issue
    if (kDebugMode) {
      debugPrint('Logger initialized in debug mode.');
    } else {
      debugPrint('Logger initialized in release mode.');
    }

    final fileOutput = FileOutput(file, clearExisting: false);

    // Initialize the file output
    await fileOutput._ensureFileExists();

    // Create and store global logger
    _globalLogger = Logger(
      level: Level.trace,
      printer: PrettyPrinter(
        methodCount: 1,
        errorMethodCount: 5,
        lineLength: 80,
        colors: false,
        printEmojis: false,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
      output: fileOutput,
    );

    // Test the logger immediately
    _globalLogger!.i(
      'Logger initialized successfully in ${kDebugMode ? 'debug' : 'production'} mode',
    );

    return _globalLogger!;
  } catch (e) {
    debugPrint('Error initializing logger: $e');
    // Return a console-only logger as fallback
    _globalLogger = Logger(
      level: Level.trace,
      printer: PrettyPrinter(
        methodCount: 1,
        errorMethodCount: 5,
        lineLength: 80,
        colors: false,
        printEmojis: false,
        dateTimeFormat: DateTimeFormat.onlyTimeAndSinceStart,
      ),
      output: ConsoleOutput(),
    );

    _globalLogger!.w('Logger fallback to console-only mode due to error: $e');
    return _globalLogger!;
  }
}

/// Force creation of log file and return its path
/// This is useful for ensuring the log file exists before export
Future<String> ensureLogFileExists() async {
  try {
    final logFile = await getLogFile();

    // Ensure parent directory exists
    final parentDir = logFile.parent;
    if (!await parentDir.exists()) {
      await parentDir.create(recursive: true);
      print('Created log directory: ${parentDir.path}');
    }

    // Create log file if it doesn't exist
    if (!await logFile.exists()) {
      await logFile.create();
      await logFile.writeAsString(
        'Log file initialized at ${DateTime.now()}\n',
      );
      print('Created log file: ${logFile.path}');
    }

    return logFile.path;
  } catch (e) {
    print('Error ensuring log file exists: $e');
    rethrow;
  }
}

/// Get log file info for debugging
Future<Map<String, dynamic>> getLogFileInfo() async {
  try {
    final logFile = await getLogFile();
    final exists = await logFile.exists();

    Map<String, dynamic> info = {
      'path': logFile.path,
      'absolutePath': logFile.absolute.path,
      'exists': exists,
      'parentExists': await logFile.parent.exists(),
      'parentPath': logFile.parent.path,
    };

    if (exists) {
      final stat = await logFile.stat();
      info.addAll({
        'size': stat.size,
        'modified': stat.modified.toIso8601String(),
        'type': stat.type.toString(),
      });
    }

    return info;
  } catch (e) {
    return {'error': e.toString()};
  }
}

/// Reset the global logger (useful for testing or reinitializing)
void resetLogger() {
  _globalLogger = null;
}

/// Get a quick logger instance for immediate use
/// This ensures we always have a working logger
Future<Logger> getQuickLogger() async {
  try {
    return await getLogger();
  } catch (e) {
    // If all else fails, return a simple console logger
    return Logger(
      level: Level.trace,
      printer: SimplePrinter(),
      output: ConsoleOutput(),
    );
  }
}
