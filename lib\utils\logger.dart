import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';

class FileOutput extends LogOutput {
  final File file;
  final bool clearExisting;

  FileOutput(this.file, {this.clearExisting = false}) {
    if (clearExisting && file.existsSync()) {
      file.deleteSync();
    }
  }

  @override
  void output(OutputEvent event) {
    for (var line in event.lines) {
      try {
        file.writeAsStringSync('$line\n', mode: FileMode.append);
      } catch (e) {
        // ignore: avoid_print
        print('Error writing to log file: $e');
      }
    }
  }
}

Future<File> getLogFile() async {
  final cacheDir = await getApplicationCacheDirectory();
  return File('${cacheDir.path}/app.log');
}

Future<Logger> getLogger({File? logFile}) async {
  final file = logFile ?? await getLogFile();

  // ignore: avoid_print
  print('Log file saved at: ${file.path}');
  // Added for debugging production issue
  if (kDebugMode) {
    print('Logger initialized in debug mode.');
  } else {
    print('Logger initialized in release mode.');
  }

  return Logger(
    level: Level.verbose,
    printer: PrettyPrinter(
      methodCount: 1,
      errorMethodCount: 5,
      lineLength: 80,
      colors: false,
      printEmojis: false,
      printTime: true,
    ),
    output: FileOutput(file, clearExisting: false),
  );
}
