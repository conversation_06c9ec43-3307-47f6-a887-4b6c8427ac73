import 'dart:convert';
import 'dart:ffi';
import 'dart:io';
import 'package:ffi/ffi.dart';
import 'package:flutter/foundation.dart';

typedef _CreateFileWNative =
    Pointer<Void> Function(
      Pointer<Utf16> lpFileName,
      Uint32 dwDesiredAccess,
      Uint32 dwShareMode,
      Pointer<Void> lpSecurityAttributes,
      Uint32 dwCreationDisposition,
      Uint32 dwFlagsAndAttributes,
      Pointer<Void> hTemplateFile,
    );
typedef _CreateFileWDart =
    Pointer<Void> Function(
      Pointer<Utf16> lpFileName,
      int dwDesiredAccess,
      int dwShareMode,
      Pointer<Void> lpSecurityAttributes,
      int dwCreationDisposition,
      int dwFlagsAndAttributes,
      Pointer<Void> hTemplateFile,
    );
typedef _ReadFileNative =
    Int32 Function(
      Pointer<Void> hFile,
      Pointer<Void> lpBuffer,
      Uint32 nNumberOfBytesToRead,
      Pointer<Uint32> lpNumberOfBytesRead,
      Pointer<Void> lpOverlapped,
    );
typedef _ReadFileDart =
    int Function(
      Pointer<Void> hFile,
      Pointer<Void> lpBuffer,
      int nNumberOfBytesToRead,
      Pointer<Uint32> lpNumberOfBytesRead,
      Pointer<Void> lpOverlapped,
    );

typedef _WriteFileNative =
    Int32 Function(
      Pointer<Void> hFile,
      Pointer<Void> lpBuffer,
      Uint32 nNumberOfBytesToWrite,
      Pointer<Uint32> lpNumberOfBytesWritten,
      Pointer<Void> lpOverlapped,
    );
typedef _WriteFileDart =
    int Function(
      Pointer<Void> hFile,
      Pointer<Void> lpBuffer,
      int nNumberOfBytesToWrite,
      Pointer<Uint32> lpNumberOfBytesWritten,
      Pointer<Void> lpOverlapped,
    );

typedef _CloseHandleNative = Int32 Function(Pointer<Void> hObject);
typedef _CloseHandleDart = int Function(Pointer<Void> hObject);

// Constants for CreateFile
// ignore: constant_identifier_names
const int GENERIC_READ = 0x80000000;
// ignore: constant_identifier_names
const int GENERIC_WRITE = 0x40000000;
// ignore: constant_identifier_names
const int OPEN_EXISTING = 3;
// ignore: constant_identifier_names
const int FILE_ATTRIBUTE_NORMAL = 0x00000080;
// ignore: constant_identifier_names
const int FILE_FLAG_OVERLAPPED = 0x40000000;

// Invalid handle value
// ignore: non_constant_identifier_names
final Pointer<Void> INVALID_HANDLE_VALUE = Pointer<Void>.fromAddress(
  0xFFFFFFFFFFFFFFFF,
);

// Load the Windows kernel32.dll
final DynamicLibrary kernel32 = DynamicLibrary.open('kernel32.dll');

// Get pointers to the native functions
final _createFileW = kernel32
    .lookupFunction<_CreateFileWNative, _CreateFileWDart>('CreateFileW');
final _readFile = kernel32.lookupFunction<_ReadFileNative, _ReadFileDart>(
  'ReadFile',
);
final _writeFile = kernel32.lookupFunction<_WriteFileNative, _WriteFileDart>(
  'WriteFile',
);
final _closeHandle = kernel32
    .lookupFunction<_CloseHandleNative, _CloseHandleDart>('CloseHandle');

class NamedPipeClientWindows {
  Pointer<Void> _pipeHandle = INVALID_HANDLE_VALUE;
  final String pipePath;

  NamedPipeClientWindows(this.pipePath);

  Future<bool> connect() async {
    if (_pipeHandle != INVALID_HANDLE_VALUE) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Already connected.');
      }
      return true;
    }

    // Attempt to open the named pipe
    _pipeHandle = _createFileW(
      pipePath.toNativeUtf16(),
      GENERIC_READ | GENERIC_WRITE,
      0, // No sharing
      nullptr, // Default security attributes
      OPEN_EXISTING,
      FILE_ATTRIBUTE_NORMAL, // Not using overlapped for simplicity
      nullptr, // No template file
    );

    if (_pipeHandle == INVALID_HANDLE_VALUE) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Failed to open named pipe: $pipePath');
      }
      return false;
    }

    if (kDebugMode) {
      print('[NamedPipeClientWindows] Connected to named pipe: $pipePath');
    }
    return true;
  }

  bool write(String message) {
    if (_pipeHandle == INVALID_HANDLE_VALUE) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Not connected to named pipe.');
      }
      return false;
    }

    // Encode the message as UTF-8 bytes
    final units = const Utf8Encoder().convert(
      '$message\n',
    ); // Add newline as delimiter
    final pointer = calloc<Uint8>(units.length);
    for (var i = 0; i < units.length; i++) {
      pointer[i] = units[i];
    }

    final bytesWritten = calloc<Uint32>();
    final success = _writeFile(
      _pipeHandle,
      pointer.cast<Void>(),
      units.length,
      bytesWritten,
      nullptr, // Not using overlapped
    );

    final writtenCount = bytesWritten.value;
    calloc.free(pointer);
    calloc.free(bytesWritten);

    if (success == 0) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Failed to write to named pipe.');
      }
      return false;
    }

    if (kDebugMode) {
      print(
        '[NamedPipeClientWindows] Wrote $writtenCount bytes to named pipe.',
      );
    }
    return true;
  }

  // Basic read implementation (can be expanded for async/overlapped I/O)
  String? read(int bufferSize) {
    if (_pipeHandle == INVALID_HANDLE_VALUE) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Not connected to named pipe.');
      }
      return null;
    }

    final buffer = calloc<Uint8>(bufferSize);
    final bytesRead = calloc<Uint32>();
    final success = _readFile(
      _pipeHandle,
      buffer.cast<Void>(),
      bufferSize,
      bytesRead,
      nullptr, // Not using overlapped
    );

    final readCount = bytesRead.value;
    String? result;
    if (success != 0 && readCount > 0) {
      result = const SystemEncoding().decode(buffer.asTypedList(readCount));
      if (kDebugMode) {
        print(
          '[NamedPipeClientWindows] Read $readCount bytes from named pipe.',
        );
      }
    } else if (success == 0) {
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Failed to read from named pipe.');
      }
    }

    calloc.free(buffer);
    calloc.free(bytesRead);

    return result;
  }

  void disconnect() {
    if (_pipeHandle != INVALID_HANDLE_VALUE) {
      _closeHandle(_pipeHandle);
      _pipeHandle = INVALID_HANDLE_VALUE;
      if (kDebugMode) {
        print('[NamedPipeClientWindows] Disconnected from named pipe.');
      }
    }
  }
}
