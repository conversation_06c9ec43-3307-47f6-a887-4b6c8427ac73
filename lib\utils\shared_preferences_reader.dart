import 'dart:io';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesReader {
  static final SharedPreferencesReader _instance =
      SharedPreferencesReader._internal();

  String? _customFilePath;

  factory SharedPreferencesReader({String? customFilePath}) {
    if (customFilePath != null) {
      _instance._customFilePath = customFilePath;
    }
    return _instance;
  }

  SharedPreferencesReader._internal();

  Map<String, dynamic>? _iptvData;

  Future<void> init() async {
    await _readData();
  }

  Future<void> _readData() async {
    final prefs = await SharedPreferences.getInstance();
    final String? dataJson = prefs.getString('flutter.iptv_data');

    if (dataJson == null) {
      debugPrint("No IPTV data found in SharedPreferences.");
      _iptvData = null;
      return;
    }

    try {
      // The data from SharedPreferences is expected to be a JSON string
      // representing a Map<String, dynamic>.
      _iptvData = json.decode(dataJson) as Map<String, dynamic>;

      final String? activationTime = prefs.getString(
        'flutter.iptv_activation_time',
      );
      final String? expirationTime = prefs.getString(
        'flutter.iptv_expiration_time',
      );

      if (activationTime != null) {
        _iptvData!['activation_time'] = activationTime;
      }
      if (expirationTime != null) {
        _iptvData!['expiration_time'] = expirationTime;
      }

      debugPrint("Successfully loaded IPTV data from SharedPreferences.");
    } catch (e) {
      debugPrint("Error decoding IPTV data from SharedPreferences: $e");
      _iptvData = null;
    }
  }

  Map<String, dynamic>? getDecryptedIptvData() {
    return _iptvData;
  }

  Future<void> rereadFile() async {
    await _readData();
  }

  Future<String?> getRawSharedPreferencesContent() async {
    final String filePath;
    if (_customFilePath != null) {
      filePath = _customFilePath!;
    } else {
      final appSupportDir = await getApplicationSupportDirectory();
      filePath =
          '${appSupportDir.path}${Platform.pathSeparator}shared_preferences.json';
    }

    try {
      final file = File(filePath);
      if (await file.exists()) {
        return await file.readAsString();
      }
    } catch (e) {
      debugPrint("Error reading raw shared_preferences.json: $e");
    }
    return null;
  }
}
