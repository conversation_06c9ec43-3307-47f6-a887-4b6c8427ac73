import 'dart:ffi';
import 'package:win32/win32.dart';
import 'package:flutter/foundation.dart'; // Import for kDebugMode
import 'package:ffi/ffi.dart'; // Required for calloc

class WindowManagerWindows {
  static const int gwlStyle = -16;
  static const int wsPopup = 0x80000000;
  static const int wsOverlappedWindow = 0x00CB0000;
  static const int swpShowWindow = 0x0040;

  static final Pointer<RECT> _originalRect = calloc<RECT>();
  static int _originalStyle = 0;

  static void enterFullscreen() {
    final hWnd = GetForegroundWindow();
    if (hWnd != 0) {
      _originalStyle = GetWindowLongPtr(hWnd, gwlStyle);
      GetWindowRect(hWnd, _originalRect);

      SetWindowLongPtr(hWnd, gwlStyle, wsPopup);
      SetWindowPos(
        hWnd,
        HWND_TOP,
        0,
        0,
        GetSystemMetrics(SM_CXSCREEN),
        GetSystemMetrics(SM_CYSCREEN),
        swpShowWindow,
      );
      if (kDebugMode) {
        print("Windows: Entering fullscreen.");
      }
    } else {
      if (kDebugMode) {
        print("Windows: Could not get active window handle for fullscreen.");
      }
    }
  }

  static void exitFullscreen() {
    final hWnd = GetForegroundWindow();
    if (hWnd != 0) {
      SetWindowLongPtr(hWnd, gwlStyle, _originalStyle);
      SetWindowPos(
        hWnd,
        HWND_NOTOPMOST,
        _originalRect.ref.left,
        _originalRect.ref.top,
        _originalRect.ref.right - _originalRect.ref.left,
        _originalRect.ref.bottom - _originalRect.ref.top,
        swpShowWindow,
      );
      if (kDebugMode) {
        print("Windows: Exiting fullscreen.");
      }
    } else {
      if (kDebugMode) {
        print(
          "Windows: Could not get active window handle for exit fullscreen.",
        );
      }
    }
  }
}
