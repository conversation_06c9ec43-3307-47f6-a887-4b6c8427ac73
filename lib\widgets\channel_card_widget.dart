import 'package:flutter/material.dart';
import 'package:country_flags/country_flags.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/utils/display_mode.dart';
import 'package:cat_tv/utils/image_cache_config.dart';

class ChannelCardWidget extends StatefulWidget {
  final Channel channel;
  final DisplayMode mode;
  final Future<void> Function(BuildContext, Channel) onChannelTap;
  final VoidCallback onToggleFavorite;

  const ChannelCardWidget({
    super.key,
    required this.channel,
    required this.mode,
    required this.onChannelTap,
    required this.onToggleFavorite,
  });

  @override
  State<ChannelCardWidget> createState() => _ChannelCardWidgetState();
}

class _ChannelCardWidgetState extends State<ChannelCardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 150),
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onTapDown(TapDownDetails details) {
    _controller.forward();
  }

  void _onTapUp(TapUpDetails details) {
    _controller.reverse();
  }

  void _onTapCancel() {
    _controller.reverse();
  }

  Future<void> _onTap() async {
    await widget.onChannelTap(context, widget.channel);
  }

  @override
  Widget build(BuildContext context) {
    final bool isList = widget.mode == DisplayMode.list;
    final double imageSize = isList ? 50.0 : 40.0;
    final double fontSize = isList ? 14 : 10;
    final double favoriteIconSize = isList ? 24 : 20;
    final double padding = isList ? 8.0 : 4.0;
    final double borderRadius = isList ? 12.0 : 8.0;

    return ScaleTransition(
      scale: _scaleAnimation,
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(borderRadius),
        child: GestureDetector(
          onTapDown: _onTapDown,
          onTapUp: _onTapUp,
          onTapCancel: _onTapCancel,
          onTap: _onTap,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(borderRadius),
              border: Border.all(
                color: Colors.white.withValues(alpha: .2),
                width: 1.0,
              ),
            ),
            child: Padding(
              padding: EdgeInsets.all(padding),
              child:
                  isList
                      ? _buildListLayout(imageSize, fontSize, favoriteIconSize)
                      : _buildGridLayout(imageSize, fontSize, favoriteIconSize),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildListLayout(
    double imageSize,
    double fontSize,
    double favoriteIconSize,
  ) {
    return Row(
      children: [
        _buildLogo(imageSize),
        const SizedBox(width: 8.0),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildChannelName(fontSize),
              _buildCountryInfo(fontSize),
            ],
          ),
        ),
        _buildFavoriteButton(favoriteIconSize),
      ],
    );
  }

  Widget _buildGridLayout(
    double imageSize,
    double fontSize,
    double favoriteIconSize,
  ) {
    return Stack(
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Expanded(flex: 3, child: Center(child: _buildLogo(imageSize))),
            Expanded(
              flex: 2,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  _buildChannelName(fontSize, textAlign: TextAlign.center),
                  const SizedBox(height: 4),
                  _buildCountryInfo(
                    fontSize,
                    mainAxisAlignment: MainAxisAlignment.center,
                  ),
                ],
              ),
            ),
          ],
        ),
        Positioned(
          top: 0,
          right: 0,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(12),
            ),
            child: _buildFavoriteButton(
              favoriteIconSize * 0.8,
              padding: const EdgeInsets.all(4),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildLogo(double imageSize) {
    return widget.channel.logoUrl != null && widget.channel.logoUrl!.isNotEmpty
        ? ClipRRect(
          borderRadius: BorderRadius.circular(8.0),
          child: ImageCacheConfig.buildOptimizedImage(
            imageUrl: widget.channel.logoUrl!,
            width: imageSize,
            height: imageSize,
            fit: BoxFit.contain,
            placeholder: Icon(Icons.tv, size: imageSize, color: Colors.grey),
            errorWidget: Icon(
              Icons.image_not_supported,
              size: imageSize,
              color: Colors.grey,
            ),
          ),
        )
        : Icon(Icons.tv, size: imageSize, color: Colors.grey);
  }

  Widget _buildChannelName(double fontSize, {TextAlign? textAlign}) {
    return Text(
      widget.channel.name,
      textAlign: textAlign,
      style: TextStyle(
        fontSize: fontSize,
        fontWeight: FontWeight.bold,
        color: Colors.white,
      ),
      maxLines: widget.mode == DisplayMode.list ? 1 : 2,
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget _buildCountryInfo(
    double fontSize, {
    MainAxisAlignment mainAxisAlignment = MainAxisAlignment.start,
  }) {
    return Row(
      mainAxisAlignment: mainAxisAlignment,
      children: [
        CountryFlag.fromCountryCode(
          widget.channel.countryCode,
          height: fontSize * (widget.mode == DisplayMode.list ? 1.2 : 1.0),
          width: fontSize * (widget.mode == DisplayMode.list ? 1.6 : 1.4),
          borderRadius: widget.mode == DisplayMode.list ? 4 : 2,
        ),
        const SizedBox(width: 4),
        Text(
          widget.channel.countryCode,
          style: TextStyle(fontSize: fontSize * 0.8, color: Colors.white70),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.mode == DisplayMode.list) const SizedBox(width: 8),
      ],
    );
  }

  Widget _buildFavoriteButton(double iconSize, {EdgeInsetsGeometry? padding}) {
    return IconButton(
      icon: Icon(
        widget.channel.isFavorite ? Icons.favorite : Icons.favorite_border,
        color:
            widget.channel.isFavorite
                ? Theme.of(context).colorScheme.primary
                : Colors.white70,
        size: iconSize,
      ),
      onPressed: widget.onToggleFavorite,
      padding: padding,
      constraints:
          padding != null
              ? const BoxConstraints(minWidth: 32, minHeight: 32)
              : null,
    );
  }
}
