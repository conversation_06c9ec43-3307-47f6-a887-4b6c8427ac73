import 'package:flutter/material.dart';
import 'package:shimmer/shimmer.dart';
import 'package:cat_tv/utils/display_mode.dart'; // Import the common DisplayMode enum

class ChannelSkeletonLoader extends StatelessWidget {
  final DisplayMode displayMode;

  const ChannelSkeletonLoader({super.key, required this.displayMode});

  @override
  Widget build(BuildContext context) {
    if (displayMode == DisplayMode.list) {
      return ListView.builder(
        itemCount: 6, // Number of skeleton items to show
        itemBuilder: (context, index) {
          return _buildListItemSkeleton();
        },
      );
    } else {
      return GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 5,
          crossAxisSpacing: 8.0,
          mainAxisSpacing: 8.0,
          childAspectRatio: 1.0,
        ),
        itemCount: 15, // Number of skeleton items for grid
        itemBuilder: (context, index) {
          return _buildGridItemSkeleton();
        },
      );
    }
  }

  Widget _buildListItemSkeleton() {
    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0)),
      color: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12.0),
        child: Shimmer.fromColors(
          baseColor: Colors.white.withValues(alpha: .1),
          highlightColor: Colors.white.withValues(alpha: .05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(12.0),
            ),
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[700],
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
                const SizedBox(width: 8.0),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        width: double.infinity,
                        height: 16,
                        color: Colors.grey[700],
                      ),
                      const SizedBox(height: 4),
                      Container(
                        width: 100,
                        height: 12,
                        color: Colors.grey[700],
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8.0),
                Container(width: 24, height: 24, color: Colors.grey[700]),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGridItemSkeleton() {
    return Card(
      elevation: 4.0,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8.0)),
      color: Colors.transparent,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8.0),
        child: Shimmer.fromColors(
          baseColor: Colors.white.withValues(alpha: .1),
          highlightColor: Colors.white.withValues(alpha: .05),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: .1),
              borderRadius: BorderRadius.circular(8.0),
            ),
            padding: const EdgeInsets.all(4.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.grey[700],
                    borderRadius: BorderRadius.circular(8.0),
                  ),
                ),
                const SizedBox(height: 4.0),
                Container(
                  width: double.infinity,
                  height: 12,
                  color: Colors.grey[700],
                ),
                const SizedBox(height: 4.0),
                Container(width: 20, height: 20, color: Colors.grey[700]),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
