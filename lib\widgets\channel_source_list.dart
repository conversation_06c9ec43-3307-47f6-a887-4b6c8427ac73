import 'package:flutter/material.dart';
import 'package:cat_tv/models/channel.dart'; // Assuming Channel model might be useful for context

class ChannelSourceList extends StatelessWidget {
  final List<Map<String, dynamic>> channelSources;
  final int currentSourceIndex;
  final Function(int) onLoadSource;
  final Channel
  channel; // Added channel for potential future use (e.g., favorite toggle)

  const ChannelSourceList({
    super.key,
    required this.channelSources,
    required this.currentSourceIndex,
    required this.onLoadSource,
    required this.channel,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize:
          MainAxisSize.min, // Allow column to shrink-wrap its children
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: _buildSourceListView(context),
          ),
        ),
      ],
    );
  }

  Widget _buildSourceListView(BuildContext context) {
    return ListView.builder(
      itemCount: channelSources.length,
      itemBuilder: (context, index) {
        final source = channelSources[index];
        final isSelected = index == currentSourceIndex;
        return _buildSourceListItem(context, source, index, isSelected);
      },
    );
  }

  Widget _buildSourceListItem(
    BuildContext context,
    Map<String, dynamic> source,
    int index,
    bool isSelected,
  ) {
    return Card(
      color:
          isSelected
              ? Theme.of(context).colorScheme.secondary.withValues(alpha: .2)
              : Theme.of(context).colorScheme.primary.withValues(alpha: .1),
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      child: InkWell(
        onTap: () => onLoadSource(index),
        // Ensure large tap target
        child: Container(
          constraints: const BoxConstraints(minHeight: 48.0),
          padding: const EdgeInsets.all(12.0),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  source['name'] ?? 'Source ${index + 1}',
                  style: TextStyle(
                    color: isSelected ? Colors.white : Colors.grey,
                    fontWeight:
                        isSelected ? FontWeight.bold : FontWeight.normal,
                    fontSize: 16,
                  ),
                  overflow: TextOverflow.ellipsis, // Prevent text overflow
                ),
              ),
              const SizedBox(width: 8),
              _buildStatusIndicators(source),
              const SizedBox(width: 8),
              Icon(
                Icons.play_arrow,
                color: isSelected ? Colors.white : Colors.grey,
              ),
              // Optional: Favorite/Pin Toggle
              // IconButton(
              //   icon: Icon(
              //     Icons.favorite_border, // or Icons.favorite
              //     color: Colors.white70,
              //   ),
              //   onPressed: () {
              //     // Implement favorite toggle logic
              //   },
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIndicators(Map<String, dynamic> source) {
    final List<Widget> indicators = [];

    // Example status indicators (you'll need to map actual source data to these)
    if (source['is_hd'] == true) {
      indicators.add(_buildStatusChip('HD', Colors.blue));
    }
    if (source['is_live'] == true) {
      indicators.add(_buildStatusChip('LIVE', Colors.red));
    }
    if (source['quality'] == 'SD') {
      indicators.add(_buildStatusChip('SD', Colors.orange));
    }
    if (source['type'] == 'Backup') {
      indicators.add(_buildStatusChip('Backup', Colors.grey));
    }

    return Wrap(spacing: 4.0, runSpacing: 4.0, children: indicators);
  }

  Widget _buildStatusChip(String text, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: .7),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Text(
        text,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }
}
