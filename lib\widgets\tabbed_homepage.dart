import 'package:flutter/material.dart';
import 'package:cat_tv/models/channel.dart';
import 'package:cat_tv/models/region.dart';
import 'package:cat_tv/models/country.dart';
import 'package:cat_tv/models/language.dart';
import 'package:cat_tv/models/category.dart' as cat_model;
import 'package:cat_tv/controllers/filter_controller.dart' as filter_ctrl;
import 'package:cat_tv/widgets/filter_widget.dart';
import 'package:cat_tv/widgets/channel_card_widget.dart';
import 'package:cat_tv/utils/display_mode.dart';
import 'package:cat_tv/repositories/channel_repository.dart';
import 'package:cat_tv/models/group.dart';
import 'package:cat_tv/repositories/group_repository.dart';
import 'package:cat_tv/widgets/channel_skeleton_loader.dart';

class TabbedHomepage extends StatefulWidget {
  final List<Channel> channels;
  final ChannelRepository repository;
  final filter_ctrl.FilterController filterController;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;
  final Future<void> Function(BuildContext, Channel) onChannelTap;
  final VoidCallback onLoadMore;
  final bool isLoading;
  final bool hasMore;

  const TabbedHomepage({
    super.key,
    required this.channels,
    required this.repository,
    required this.filterController,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
    required this.onChannelTap,
    required this.onLoadMore,
    required this.isLoading,
    required this.hasMore,
  });

  @override
  State<TabbedHomepage> createState() => _TabbedHomepageState();
}

class _TabbedHomepageState extends State<TabbedHomepage>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final PageStorageKey _featuredKey = const PageStorageKey('featured_tab');
  final PageStorageKey _groupsKey = const PageStorageKey('groups_tab');
  final PageStorageKey _browseKey = const PageStorageKey('browse_tab');
  List<Group> _groups = [];
  final GroupRepository _groupRepository = GroupRepository();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadGroups();
  }

  Future<void> _loadGroups() async {
    final groups = await _groupRepository.getGroups();
    if (mounted) {
      setState(() {
        _groups = groups;
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return PageStorage(
      bucket: PageStorageBucket(),
      child: DefaultTabController(
        length: 3,
        child: Column(
          children: [
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withValues(alpha: 0.05),
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.1),
                  ),
                ),
              ),
              child: TabBar(
                controller: _tabController,
                tabs: const [
                  Tab(icon: Icon(Icons.star_outline), text: 'Featured'),
                  Tab(icon: Icon(Icons.folder_outlined), text: 'Groups'),
                  Tab(icon: Icon(Icons.explore_outlined), text: 'Browse'),
                ],
                labelColor: Colors.white,
                unselectedLabelColor: Colors.grey,
                indicatorColor: theme.colorScheme.primary,
                dividerColor: Colors.transparent,
                labelStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                unselectedLabelStyle: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _FeaturedTab(
                    key: _featuredKey,
                    channels: widget.channels,
                    onChannelTap: widget.onChannelTap,
                  ),
                  _GroupsTab(
                    key: _groupsKey,
                    groups: _groups,
                    repository: widget.repository,
                    onChannelTap: widget.onChannelTap,
                  ),
                  _BrowseTab(
                    key: _browseKey,
                    channels: widget.channels,
                    filterController: widget.filterController,
                    regions: widget.regions,
                    countries: widget.countries,
                    languages: widget.languages,
                    categories: widget.categories,
                    onChannelTap: widget.onChannelTap,
                    onLoadMore: widget.onLoadMore,
                    isLoading: widget.isLoading,
                    hasMore: widget.hasMore,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _FeaturedTab extends StatefulWidget {
  final List<Channel> channels;
  final Future<void> Function(BuildContext, Channel) onChannelTap;

  const _FeaturedTab({
    super.key,
    required this.channels,
    required this.onChannelTap,
  });

  @override
  State<_FeaturedTab> createState() => _FeaturedTabState();
}

class _FeaturedTabState extends State<_FeaturedTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final featuredChannels =
        widget.channels.where((channel) => channel.isFeatured == true).toList();

    if (featuredChannels.isEmpty) {
      return const Center(
        child: Text(
          'No featured channels available',
          style: TextStyle(color: Colors.white70),
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Padding(
          padding: EdgeInsets.all(16.0),
          child: Text(
            'Featured Channels',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
        Expanded(
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            itemCount: featuredChannels.length,
            itemBuilder: (context, index) {
              final channel = featuredChannels[index];
              return ChannelCardWidget(
                key: ValueKey(channel.channelId),
                channel: channel,
                mode: DisplayMode.list, // Changed to list view
                onChannelTap: widget.onChannelTap,
                onToggleFavorite: () async {
                  await channel.toggleFavorite();
                },
              );
            },
            separatorBuilder: (context, index) => const SizedBox(height: 8.0),
          ),
        ),
      ],
    );
  }
}

class _GroupsTab extends StatefulWidget {
  final List<Group> groups;
  final ChannelRepository repository;
  final Future<void> Function(BuildContext, Channel) onChannelTap;

  const _GroupsTab({
    super.key,
    required this.groups,
    required this.repository,
    required this.onChannelTap,
  });

  @override
  State<_GroupsTab> createState() => _GroupsTabState();
}

class _GroupsTabState extends State<_GroupsTab>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);

    if (widget.groups.isEmpty) {
      return const Center(
        child: Text(
          'No groups available',
          style: TextStyle(color: Colors.white70),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: widget.groups.length,
      itemBuilder: (context, index) {
        final group = widget.groups[index];
        return Card(
          margin: const EdgeInsets.only(bottom: 16),
          color: theme.colorScheme.surface.withValues(alpha: 0.05),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: BorderSide(
              color: theme.colorScheme.outline.withValues(alpha: 0.1),
            ),
          ),
          child: ExpansionTile(
            title: Text(
              group.name,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            subtitle: Text(
              '${group.channelCount} channels',
              style: const TextStyle(color: Colors.grey, fontSize: 14),
            ),
            iconColor: theme.colorScheme.primary,
            collapsedIconColor: Colors.grey,
            children: [_buildChannelListForGroup(group)],
          ),
        );
      },
    );
  }

  Widget _buildChannelListForGroup(Group group) {
    return FutureBuilder<List<Channel>>(
      future: widget.repository.getChannelsByGroupId(group.id),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(child: Text('No channels in this group.'));
        }

        final channels = snapshot.data!;
        return ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          itemCount: channels.length,
          itemBuilder: (context, index) {
            final channel = channels[index];
            return ChannelCardWidget(
              key: ValueKey(channel.channelId),
              channel: channel,
              mode: DisplayMode.list,
              onChannelTap: widget.onChannelTap,
              onToggleFavorite: () async {
                await channel.toggleFavorite();
              },
            );
          },
          separatorBuilder: (context, index) => const SizedBox(height: 8.0),
        );
      },
    );
  }
}

class _BrowseTab extends StatefulWidget {
  final List<Channel> channels;
  final filter_ctrl.FilterController filterController;
  final List<Region> regions;
  final List<Country> countries;
  final List<Language> languages;
  final List<cat_model.Category> categories;
  final Future<void> Function(BuildContext, Channel) onChannelTap;
  final VoidCallback onLoadMore;
  final bool isLoading;
  final bool hasMore;

  const _BrowseTab({
    super.key,
    required this.channels,
    required this.filterController,
    required this.regions,
    required this.countries,
    required this.languages,
    required this.categories,
    required this.onChannelTap,
    required this.onLoadMore,
    required this.isLoading,
    required this.hasMore,
  });

  @override
  State<_BrowseTab> createState() => _BrowseTabState();
}

class _BrowseTabState extends State<_BrowseTab>
    with AutomaticKeepAliveClientMixin {
  DisplayMode _displayMode = DisplayMode.list;
  late ScrollController _scrollController;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      if (widget.hasMore && !widget.isLoading) {
        widget.onLoadMore();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final theme = Theme.of(context);
    final isMobile = MediaQuery.of(context).size.width < 600;

    return Column(
      children: [
        // Filter widget at the top
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: MinimalistFilterWidget(
            controller: widget.filterController,
            regions: widget.regions,
            countries: widget.countries,
            languages: widget.languages,
            categories: widget.categories,
          ),
        ),

        // Display mode toggle
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${widget.channels.length} channels',
                style: TextStyle(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontSize: 14,
                ),
              ),
              SegmentedButton<DisplayMode>(
                segments: const <ButtonSegment<DisplayMode>>[
                  ButtonSegment<DisplayMode>(
                    value: DisplayMode.list,
                    icon: Icon(Icons.view_list_rounded, size: 18),
                  ),
                  ButtonSegment<DisplayMode>(
                    value: DisplayMode.smallTiles,
                    icon: Icon(Icons.apps_rounded, size: 18),
                  ),
                ],
                selected: <DisplayMode>{_displayMode},
                onSelectionChanged: (Set<DisplayMode> newSelection) {
                  setState(() {
                    _displayMode = newSelection.first;
                  });
                },
                style: SegmentedButton.styleFrom(
                  foregroundColor: Colors.grey,
                  selectedForegroundColor: Colors.white,
                  selectedBackgroundColor: theme.colorScheme.primary.withValues(
                    alpha: 0.1,
                  ),
                  side: BorderSide(
                    color: theme.colorScheme.outline.withValues(alpha: 0.3),
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ),
        ),

        const SizedBox(height: 16),

        // Channel list
        Expanded(child: _buildChannelList(context, theme, isMobile)),
      ],
    );
  }

  Widget _buildChannelList(
    BuildContext context,
    ThemeData theme,
    bool isMobile,
  ) {
    if (widget.channels.isEmpty && widget.isLoading) {
      return ChannelSkeletonLoader(displayMode: _displayMode);
    }

    if (widget.channels.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.tv_off,
              size: 64,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No channels found',
              style: TextStyle(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    final Widget loadingIndicator = Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: CircularProgressIndicator(color: theme.colorScheme.primary),
      ),
    );

    if (_displayMode == DisplayMode.list) {
      return ListView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        itemCount: widget.channels.length + (widget.hasMore ? 1 : 0),
        itemExtent: 88, // Fixed height for performance (80 + 8 for spacing)
        cacheExtent: 500.0,
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: true,
        addSemanticIndexes: false,
        itemBuilder: (context, index) {
          if (index == widget.channels.length) {
            return loadingIndicator;
          }
          final channel = widget.channels[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 8.0),
            child: RepaintBoundary(
              child: ChannelCardWidget(
                key: ValueKey(channel.channelId),
                channel: channel,
                mode: _displayMode,
                onChannelTap: widget.onChannelTap,
                onToggleFavorite: () async {
                  await channel.toggleFavorite();
                  if (mounted) {
                    setState(() {});
                  }
                },
              ),
            ),
          );
        },
      );
    } else {
      return GridView.builder(
        controller: _scrollController,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: isMobile ? 2 : 5,
          crossAxisSpacing: 8.0,
          mainAxisSpacing: 8.0,
          childAspectRatio: 0.8,
        ),
        itemCount: widget.channels.length + (widget.hasMore ? 1 : 0),
        cacheExtent: 800.0,
        addAutomaticKeepAlives: false,
        addRepaintBoundaries: true,
        addSemanticIndexes: false,
        itemBuilder: (context, index) {
          if (index == widget.channels.length) {
            return loadingIndicator;
          }
          final channel = widget.channels[index];
          return RepaintBoundary(
            child: ChannelCardWidget(
              key: ValueKey(channel.channelId),
              channel: channel,
              mode: _displayMode,
              onChannelTap: widget.onChannelTap,
              onToggleFavorite: () async {
                await channel.toggleFavorite();
                if (mounted) {
                  setState(() {});
                }
              },
            ),
          );
        },
      );
    }
  }
}
