import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cat_tv/utils/logger.dart';

void main() {
  group('Logger Tests', () {
    test('should create log file in production mode', () async {
      // Test log file creation
      final logFilePath = await ensureLogFileExists();
      expect(logFilePath, isNotNull);
      expect(logFilePath, isNotEmpty);
      
      final logFile = File(logFilePath);
      expect(await logFile.exists(), isTrue);
      
      print('Log file created at: $logFilePath');
    });

    test('should write to log file', () async {
      final logger = await getLogger();
      
      logger.i('Test info message');
      logger.w('Test warning message');
      logger.e('Test error message');
      
      // Wait a bit for file writes
      await Future.delayed(Duration(milliseconds: 100));
      
      final logFile = await getLogFile();
      final content = await logFile.readAsString();
      
      expect(content, contains('Test info message'));
      expect(content, contains('Test warning message'));
      expect(content, contains('Test error message'));
      
      print('Log file content length: ${content.length}');
    });

    test('should get log file info', () async {
      final info = await getLogFileInfo();
      
      expect(info, isNotNull);
      expect(info['path'], isNotNull);
      expect(info['exists'], isTrue);
      
      print('Log file info: $info');
    });

    test('should handle missing log file gracefully', () async {
      final logFile = await getLogFile();
      
      // Delete the log file if it exists
      if (await logFile.exists()) {
        await logFile.delete();
      }
      
      // Ensure it gets recreated
      final newLogFilePath = await ensureLogFileExists();
      expect(await File(newLogFilePath).exists(), isTrue);
      
      print('Log file recreated at: $newLogFilePath');
    });
  });
}
