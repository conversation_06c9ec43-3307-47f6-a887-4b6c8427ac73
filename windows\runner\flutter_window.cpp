#include "flutter_window.h"

#include <optional>
#include <windows.h> // For ShowWindow, IsZoomed, GetSystemMetrics, SetWindowPos, GetWindowLong, SetWindowLong

#include "flutter/generated_plugin_registrant.h"
#include "flutter/method_channel.h" // For MethodChannel
#include "flutter/standard_method_codec.h" // Explicitly include for StandardMethodCodec

FlutterWindow::FlutterWindow(const flutter::DartProject& project)
    : project_(project) {}

FlutterWindow::~FlutterWindow() {}

bool FlutterWindow::OnCreate() {
  if (!Win32Window::OnCreate()) {
    return false;
  }

  RECT frame = GetClientArea();

  // The size here must match the window dimensions to avoid unnecessary surface
  // creation / destruction in the startup path.
  flutter_controller_ = std::make_unique<flutter::FlutterViewController>(
      frame.right - frame.left, frame.bottom - frame.top, project_);
  // Ensure that basic setup of the controller was successful.
  if (!flutter_controller_->engine() || !flutter_controller_->view()) {
    return false;
  }
  RegisterPlugins(flutter_controller_->engine());
  SetChildContent(flutter_controller_->view()->GetNativeWindow());

  // --- START: Platform Channel Setup ---
  const flutter::StandardMethodCodec* codec = &flutter::StandardMethodCodec::GetInstance();
  flutter::MethodChannel<flutter::EncodableValue>* channel =
      new flutter::MethodChannel<flutter::EncodableValue>(
          flutter_controller_->engine()->messenger(),
          "com.cat_tv/window_manager",
          codec);

  channel->SetMethodCallHandler(
      [this](const flutter::MethodCall<flutter::EncodableValue>& call,
             std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
        if (call.method_name().compare("toggleFullScreen") == 0) {
          HWND hwnd = this->GetHandle();
          if (!is_fullscreen_) {
            // Enter fullscreen by maximizing the window
            ShowWindow(hwnd, SW_MAXIMIZE);
            is_fullscreen_ = true;
          } else {
            // Exit fullscreen by restoring the window
            ShowWindow(hwnd, SW_RESTORE);
            is_fullscreen_ = false;
          }
          result->Success();
        } else {
          result->NotImplemented();
        }
      });
  // --- END: Platform Channel Setup ---

  flutter_controller_->engine()->SetNextFrameCallback([&]() {
    this->Show();
  });

  // Flutter can complete the first frame before the "show window" callback is
  // registered. The following call ensures a frame is pending to ensure the
  // window is shown. It is a no-op if the first frame hasn't completed yet.
  flutter_controller_->ForceRedraw();

  return true;
}

void FlutterWindow::OnDestroy() {
  if (flutter_controller_) {
    // The FlutterViewController handles the engine shutdown.
    flutter_controller_ = nullptr;
  }

  Win32Window::OnDestroy();
}

LRESULT
FlutterWindow::MessageHandler(HWND hwnd, UINT const message,
                              WPARAM const wparam,
                              LPARAM const lparam) noexcept {
  // Give Flutter, including plugins, an opportunity to handle window messages.
  if (flutter_controller_) {
    std::optional<LRESULT> result =
        flutter_controller_->HandleTopLevelWindowProc(hwnd, message, wparam,
                                                      lparam);
    if (result) {
      return *result;
    }
  }

  switch (message) {
    case WM_FONTCHANGE:
      flutter_controller_->engine()->ReloadSystemFonts();
      break;
  }

  return Win32Window::MessageHandler(hwnd, message, wparam, lparam);
}
